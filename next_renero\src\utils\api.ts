'use client';

// Temel API yanıt tipi
export interface ApiResponse<T = unknown> {
  success: boolean;
  data: T;
  message?: string;
}

/**
 * API istekleri için yardımcı fonksiyon
 * @param endpoint - API endpoint yolu (örn: '/navigation.php')
 * @param params - URL parametreleri
 * @returns Tam API URL'si
 */
export function getApiUrl(endpoint: string, params?: Record<string, string>) {
  // BASE_API_URL değerini çevre değişkeninden al - main API için
  const baseApiUrl = process.env.NEXT_PUBLIC_API_URL || (
    process.env.NODE_ENV === 'production'
      ? 'https://renero.posterro.com/api'
      : '/api'
  );
  


  // Endpoint'i tam URL'ye dönüştür
  let url = `${baseApiUrl}${endpoint}`;

  // Parametreler varsa URL'ye ekle
  if (params && Object.keys(params).length > 0) {
    const queryString = Object.entries(params)
      .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
      .join('&');

    url = `${url}?${queryString}`;
  }

  return url;
}

/**
 * API'den veri getirmek için yardımcı fonksiyon
 * @param endpoint - API endpoint yolu (örn: '/navigation.php')
 * @param params - URL parametreleri
 * @returns API yanıtı
 */
export async function fetchFromApi<T = unknown>(endpoint: string, params?: Record<string, string>): Promise<ApiResponse<T>> {
  // Mock veri sistemini devre dışı bırak - gerçek API kullan
  console.log('Production mode: Using real API for', endpoint);

  const url = getApiUrl(endpoint, params);

  try {
    const response = await fetch(url);

    if (!response.ok) {
      throw new Error(`API error: ${response.status}`);
    }

    const data: ApiResponse<T> = await response.json();
    return data;
  } catch (error) {
    console.error('API fetch error:', error);
    throw error;
  }
}

/**
 * Admin API istekleri için yardımcı fonksiyon
 * @param endpoint - API endpoint yolu (örn: '/users.php')
 * @param options - Fetch options (method, body, headers)
 * @returns API yanıtı
 */
export async function fetchFromAdminApi<T = unknown>(
  endpoint: string,
  options?: RequestInit
): Promise<ApiResponse<T>> {
  // Admin API base URL
  const baseApiUrl = process.env.NODE_ENV === 'production'
    ? 'https://renero.posterro.com/api/admin'
    : 'http://localhost:8080/admin';

  const url = `${baseApiUrl}${endpoint}`;

  console.log('Admin API request:', url);

  try {
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options?.headers,
      },
      ...options,
    });

    if (!response.ok) {
      throw new Error(`Admin API error: ${response.status}`);
    }

    const data: ApiResponse<T> = await response.json();
    return data;
  } catch (error) {
    console.error('Admin API fetch error:', error);
    throw error;
  }
}
