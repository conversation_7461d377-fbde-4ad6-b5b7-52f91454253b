/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/homepage/page",{

/***/ "(app-pages-browser)/./src/app/admin/homepage/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/admin/homepage/page.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomepagePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_admin_auth_WithAuth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/admin/auth/WithAuth */ \"(app-pages-browser)/./src/components/admin/auth/WithAuth.tsx\");\n/* harmony import */ var _components_admin_layout_AdminLayout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/admin/layout/AdminLayout */ \"(app-pages-browser)/./src/components/admin/layout/AdminLayout.tsx\");\n/* harmony import */ var _utils_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/api */ \"(app-pages-browser)/./src/utils/api.ts\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_components_ui_Button__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _components_ui_Modal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/Modal */ \"(app-pages-browser)/./src/components/ui/Modal.tsx\");\n/* harmony import */ var _components_ui_Modal__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(_components_ui_Modal__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowUpIcon,PencilIcon,PhotoIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowUpIcon,PencilIcon,PhotoIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PhotoIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowUpIcon,PencilIcon,PhotoIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowUpIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowUpIcon,PencilIcon,PhotoIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowUpIcon,PencilIcon,PhotoIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowUpIcon,PencilIcon,PhotoIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction HomepagePage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_auth_WithAuth__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HomepageContent, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n            lineNumber: 45,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n        lineNumber: 44,\n        columnNumber: 5\n    }, this);\n}\n_c = HomepagePage;\nfunction HomepageContent() {\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('hero');\n    const [heroSlides, setHeroSlides] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [services, setServices] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isModalOpen, setIsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingItem, setEditingItem] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomepageContent.useEffect\": ()=>{\n            fetchData();\n        }\n    }[\"HomepageContent.useEffect\"], []);\n    const fetchData = async ()=>{\n        try {\n            const [heroResponse, servicesResponse] = await Promise.all([\n                (0,_utils_api__WEBPACK_IMPORTED_MODULE_4__.fetchFromAdminApi)('/hero-slides.php'),\n                (0,_utils_api__WEBPACK_IMPORTED_MODULE_4__.fetchFromAdminApi)('/services.php')\n            ]);\n            if (heroResponse.success && heroResponse.data) {\n                setHeroSlides(heroResponse.data);\n            }\n            if (servicesResponse.success && servicesResponse.data) {\n                setServices(servicesResponse.data);\n            }\n        } catch (error) {\n            console.error('Veri yüklenirken hata:', error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error('Veriler yüklenirken hata oluştu');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleAddNew = ()=>{\n        if (activeTab === 'hero') {\n            setEditingItem({\n                id: 0,\n                title_tr: '',\n                title_en: '',\n                subtitle_tr: '',\n                subtitle_en: '',\n                image_path: '',\n                sort_order: heroSlides.length + 1,\n                status: 'active'\n            });\n        } else {\n            setEditingItem({\n                id: 0,\n                title_tr: '',\n                title_en: '',\n                description_tr: '',\n                description_en: '',\n                icon: 'atom',\n                featured: false,\n                sort_order: services.length + 1,\n                status: 'active'\n            });\n        }\n        setIsModalOpen(true);\n    };\n    const handleEdit = (item)=>{\n        setEditingItem(item);\n        setIsModalOpen(true);\n    };\n    const handleDelete = async (id)=>{\n        if (!confirm('Bu öğeyi silmek istediğinizden emin misiniz?')) return;\n        try {\n            const endpoint = activeTab === 'hero' ? '/hero-slides.php' : '/services.php';\n            const response = await (0,_utils_api__WEBPACK_IMPORTED_MODULE_4__.fetchFromAdminApi)(endpoint, {\n                method: 'DELETE',\n                body: JSON.stringify({\n                    id\n                })\n            });\n            if (response.success) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].success('Öğe başarıyla silindi');\n                fetchData();\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error(response.message || 'Silme işlemi başarısız');\n            }\n        } catch (error) {\n            console.error('Silme hatası:', error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error('Silme işlemi sırasında hata oluştu');\n        }\n    };\n    const handleSave = async (formData)=>{\n        try {\n            const endpoint = activeTab === 'hero' ? '/hero-slides.php' : '/services.php';\n            const method = (editingItem === null || editingItem === void 0 ? void 0 : editingItem.id) ? 'PUT' : 'POST';\n            const response = await (0,_utils_api__WEBPACK_IMPORTED_MODULE_4__.fetchFromAdminApi)(endpoint, {\n                method,\n                body: JSON.stringify(formData)\n            });\n            if (response.success) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].success((editingItem === null || editingItem === void 0 ? void 0 : editingItem.id) ? 'Güncelleme başarılı' : 'Ekleme başarılı');\n                setIsModalOpen(false);\n                setEditingItem(null);\n                fetchData();\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error(response.message || 'İşlem başarısız');\n            }\n        } catch (error) {\n            console.error('Kaydetme hatası:', error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error('Kaydetme sırasında hata oluştu');\n        }\n    };\n    const moveItem = async (id, direction)=>{\n        try {\n            const endpoint = activeTab === 'hero' ? '/hero-slides.php' : '/services.php';\n            const response = await (0,_utils_api__WEBPACK_IMPORTED_MODULE_4__.fetchFromAdminApi)(\"\".concat(endpoint, \"?action=reorder\"), {\n                method: 'POST',\n                body: JSON.stringify({\n                    id,\n                    direction\n                })\n            });\n            if (response.success) {\n                fetchData();\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error('Sıralama değiştirilemedi');\n            }\n        } catch (error) {\n            console.error('Sıralama hatası:', error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error('Sıralama değiştirilirken hata oluştu');\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_layout_AdminLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                    lineNumber: 186,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                lineNumber: 185,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n            lineNumber: 184,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_layout_AdminLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"px-4 sm:px-6 lg:px-8 py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"sm:flex sm:items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"sm:flex-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-xl font-semibold text-secondary-900\",\n                                    children: \"Ana Sayfa Y\\xf6netimi\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                    lineNumber: 197,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-2 text-sm text-secondary-700\",\n                                    children: \"Ana sayfadaki slider ve hizmetler b\\xf6l\\xfcm\\xfcn\\xfc buradan y\\xf6netebilirsiniz.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                            lineNumber: 196,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 sm:ml-16 sm:mt-0 sm:flex-none\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                onClick: handleAddNew,\n                                className: \"bg-primary-600 hover:bg-primary-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Yeni Ekle\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                            lineNumber: 202,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                    lineNumber: 195,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-b border-secondary-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"-mb-px flex space-x-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setActiveTab('hero'),\n                                    className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'hero' ? 'border-primary-500 text-primary-600' : 'border-transparent text-secondary-500 hover:text-secondary-700 hover:border-secondary-300'),\n                                    children: \"Hero Slider\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setActiveTab('services'),\n                                    className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'services' ? 'border-primary-500 text-primary-600' : 'border-transparent text-secondary-500 hover:text-secondary-700 hover:border-secondary-300'),\n                                    children: \"Hizmetler\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                    lineNumber: 214,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8\",\n                    children: activeTab === 'hero' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HeroSlidesTable, {\n                        slides: heroSlides,\n                        onEdit: handleEdit,\n                        onDelete: handleDelete,\n                        onMove: moveItem\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                        lineNumber: 244,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ServicesTable, {\n                        services: services,\n                        onEdit: handleEdit,\n                        onDelete: handleDelete,\n                        onMove: moveItem\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                        lineNumber: 251,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                    lineNumber: 242,\n                    columnNumber: 9\n                }, this),\n                isModalOpen && editingItem && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EditModal, {\n                    item: editingItem,\n                    type: activeTab,\n                    onSave: handleSave,\n                    onClose: ()=>{\n                        setIsModalOpen(false);\n                        setEditingItem(null);\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                    lineNumber: 262,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n            lineNumber: 194,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n        lineNumber: 193,\n        columnNumber: 5\n    }, this);\n}\n_s(HomepageContent, \"uF0MXxevmKtMRoNzBPscUVV1hOU=\");\n_c1 = HomepageContent;\n// Hero Slides Table Component\nfunction HeroSlidesTable(param) {\n    let { slides, onEdit, onDelete, onMove } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white shadow rounded-lg overflow-hidden\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n            className: \"min-w-full divide-y divide-secondary-200\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                    className: \"bg-secondary-50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                className: \"px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider\",\n                                children: \"G\\xf6rsel\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                lineNumber: 294,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                className: \"px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider\",\n                                children: \"Başlık\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                lineNumber: 297,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                className: \"px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider\",\n                                children: \"Durum\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                lineNumber: 300,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                className: \"px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider\",\n                                children: \"Sıra\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                lineNumber: 303,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                className: \"px-6 py-3 text-right text-xs font-medium text-secondary-500 uppercase tracking-wider\",\n                                children: \"İşlemler\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                lineNumber: 306,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                        lineNumber: 293,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                    lineNumber: 292,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                    className: \"bg-white divide-y divide-secondary-200\",\n                    children: slides.map((slide, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-16 w-24 bg-secondary-100 rounded-lg overflow-hidden\",\n                                        children: slide.image_path ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: slide.image_path,\n                                            alt: slide.title_tr,\n                                            className: \"h-full w-full object-cover\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                            lineNumber: 317,\n                                            columnNumber: 21\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-full w-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-8 w-8 text-secondary-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                                lineNumber: 324,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                            lineNumber: 323,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                    lineNumber: 314,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm font-medium text-secondary-900\",\n                                            children: slide.title_tr\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                            lineNumber: 330,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-secondary-500 truncate max-w-xs\",\n                                            children: slide.subtitle_tr\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                            lineNumber: 333,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                    lineNumber: 329,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(slide.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'),\n                                        children: slide.status === 'active' ? 'Aktif' : 'Pasif'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                        lineNumber: 338,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                    lineNumber: 337,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap text-sm text-secondary-500\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: slide.sort_order\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                                lineNumber: 348,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col space-y-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>onMove(slide.id, 'up'),\n                                                        disabled: index === 0,\n                                                        className: \"p-1 text-secondary-400 hover:text-secondary-600 disabled:opacity-50\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                                            lineNumber: 355,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                                        lineNumber: 350,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>onMove(slide.id, 'down'),\n                                                        disabled: index === slides.length - 1,\n                                                        className: \"p-1 text-secondary-400 hover:text-secondary-600 disabled:opacity-50\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                                            lineNumber: 362,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                                        lineNumber: 357,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                                lineNumber: 349,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                        lineNumber: 347,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                    lineNumber: 346,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-end space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>onEdit(slide),\n                                                className: \"text-primary-600 hover:text-primary-900\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                                    lineNumber: 373,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                                lineNumber: 369,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>onDelete(slide.id),\n                                                className: \"text-red-600 hover:text-red-900\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                                    lineNumber: 379,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                                lineNumber: 375,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                        lineNumber: 368,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                    lineNumber: 367,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, slide.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                            lineNumber: 313,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                    lineNumber: 311,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n            lineNumber: 291,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n        lineNumber: 290,\n        columnNumber: 5\n    }, this);\n}\n_c2 = HeroSlidesTable;\n// Services Table Component\nfunction ServicesTable(param) {\n    let { services, onEdit, onDelete, onMove } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white shadow rounded-lg overflow-hidden\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n            className: \"min-w-full divide-y divide-secondary-200\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                    className: \"bg-secondary-50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                className: \"px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider\",\n                                children: \"İkon\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                lineNumber: 408,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                className: \"px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider\",\n                                children: \"Başlık\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                lineNumber: 411,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                className: \"px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider\",\n                                children: \"\\xd6ne \\xc7ıkan\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                lineNumber: 414,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                className: \"px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider\",\n                                children: \"Durum\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                lineNumber: 417,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                className: \"px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider\",\n                                children: \"Sıra\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                lineNumber: 420,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                className: \"px-6 py-3 text-right text-xs font-medium text-secondary-500 uppercase tracking-wider\",\n                                children: \"İşlemler\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                lineNumber: 423,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                        lineNumber: 407,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                    lineNumber: 406,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                    className: \"bg-white divide-y divide-secondary-200\",\n                    children: services.map((service, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-10 w-10 bg-primary-100 rounded-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-primary-600 font-semibold text-sm\",\n                                            children: service.icon.charAt(0).toUpperCase()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                            lineNumber: 433,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                        lineNumber: 432,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                    lineNumber: 431,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm font-medium text-secondary-900\",\n                                            children: service.title_tr\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                            lineNumber: 439,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-secondary-500 truncate max-w-xs\",\n                                            children: service.description_tr\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                            lineNumber: 442,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                    lineNumber: 438,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(service.featured ? 'bg-yellow-100 text-yellow-800' : 'bg-secondary-100 text-secondary-800'),\n                                        children: service.featured ? 'Evet' : 'Hayır'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                        lineNumber: 447,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                    lineNumber: 446,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(service.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'),\n                                        children: service.status === 'active' ? 'Aktif' : 'Pasif'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                        lineNumber: 456,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                    lineNumber: 455,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap text-sm text-secondary-500\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: service.sort_order\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                                lineNumber: 466,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col space-y-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>onMove(service.id, 'up'),\n                                                        disabled: index === 0,\n                                                        className: \"p-1 text-secondary-400 hover:text-secondary-600 disabled:opacity-50\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                                            lineNumber: 473,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                                        lineNumber: 468,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>onMove(service.id, 'down'),\n                                                        disabled: index === services.length - 1,\n                                                        className: \"p-1 text-secondary-400 hover:text-secondary-600 disabled:opacity-50\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                                            lineNumber: 480,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                                        lineNumber: 475,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                                lineNumber: 467,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                        lineNumber: 465,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                    lineNumber: 464,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-end space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>onEdit(service),\n                                                className: \"text-primary-600 hover:text-primary-900\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                                    lineNumber: 491,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                                lineNumber: 487,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>onDelete(service.id),\n                                                className: \"text-red-600 hover:text-red-900\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                                    lineNumber: 497,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                                lineNumber: 493,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                        lineNumber: 486,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                    lineNumber: 485,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, service.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                            lineNumber: 430,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                    lineNumber: 428,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n            lineNumber: 405,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n        lineNumber: 404,\n        columnNumber: 5\n    }, this);\n}\n_c3 = ServicesTable;\n// Edit Modal Component\nfunction EditModal(param) {\n    let { item, type, onSave, onClose } = param;\n    _s1();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(item);\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        onSave(formData);\n    };\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Modal__WEBPACK_IMPORTED_MODULE_7__.Modal, {\n        isOpen: true,\n        onClose: onClose,\n        title: \"\".concat(item.id ? 'Düzenle' : 'Yeni Ekle', \" - \").concat(type === 'hero' ? 'Slider' : 'Hizmet'),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n            onSubmit: handleSubmit,\n            className: \"space-y-6\",\n            children: [\n                type === 'hero' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 gap-6 sm:grid-cols-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-secondary-700\",\n                                            children: \"Başlık (TR)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                            lineNumber: 542,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: formData.title_tr,\n                                            onChange: (e)=>handleInputChange('title_tr', e.target.value),\n                                            className: \"mt-1 block w-full rounded-md border-secondary-300 shadow-sm focus:border-primary-500 focus:ring-primary-500\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                            lineNumber: 545,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                    lineNumber: 541,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-secondary-700\",\n                                            children: \"Başlık (EN)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                            lineNumber: 554,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: formData.title_en,\n                                            onChange: (e)=>handleInputChange('title_en', e.target.value),\n                                            className: \"mt-1 block w-full rounded-md border-secondary-300 shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                            lineNumber: 557,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                    lineNumber: 553,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                            lineNumber: 540,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 gap-6 sm:grid-cols-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-secondary-700\",\n                                            children: \"Alt Başlık (TR)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                            lineNumber: 567,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            rows: 3,\n                                            value: formData.subtitle_tr,\n                                            onChange: (e)=>handleInputChange('subtitle_tr', e.target.value),\n                                            className: \"mt-1 block w-full rounded-md border-secondary-300 shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                            lineNumber: 570,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                    lineNumber: 566,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-secondary-700\",\n                                            children: \"Alt Başlık (EN)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                            lineNumber: 578,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            rows: 3,\n                                            value: formData.subtitle_en,\n                                            onChange: (e)=>handleInputChange('subtitle_en', e.target.value),\n                                            className: \"mt-1 block w-full rounded-md border-secondary-300 shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                            lineNumber: 581,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                    lineNumber: 577,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                            lineNumber: 565,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-secondary-700\",\n                                    children: \"G\\xf6rsel URL\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                    lineNumber: 590,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"url\",\n                                    value: formData.image_path,\n                                    onChange: (e)=>handleInputChange('image_path', e.target.value),\n                                    className: \"mt-1 block w-full rounded-md border-secondary-300 shadow-sm focus:border-primary-500 focus:ring-primary-500\",\n                                    placeholder: \"https://example.com/image.jpg\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                    lineNumber: 593,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                            lineNumber: 589,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 gap-6 sm:grid-cols-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-secondary-700\",\n                                            children: \"Başlık (TR)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                            lineNumber: 606,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: formData.title_tr,\n                                            onChange: (e)=>handleInputChange('title_tr', e.target.value),\n                                            className: \"mt-1 block w-full rounded-md border-secondary-300 shadow-sm focus:border-primary-500 focus:ring-primary-500\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                            lineNumber: 609,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                    lineNumber: 605,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-secondary-700\",\n                                            children: \"Başlık (EN)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                            lineNumber: 618,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: formData.title_en,\n                                            onChange: (e)=>handleInputChange('title_en', e.target.value),\n                                            className: \"mt-1 block w-full rounded-md border-secondary-300 shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                            lineNumber: 621,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                    lineNumber: 617,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                            lineNumber: 604,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 gap-6 sm:grid-cols-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-secondary-700\",\n                                            children: \"A\\xe7ıklama (TR)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                            lineNumber: 631,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            rows: 3,\n                                            value: formData.description_tr,\n                                            onChange: (e)=>handleInputChange('description_tr', e.target.value),\n                                            className: \"mt-1 block w-full rounded-md border-secondary-300 shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                            lineNumber: 634,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                    lineNumber: 630,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-secondary-700\",\n                                            children: \"A\\xe7ıklama (EN)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                            lineNumber: 642,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            rows: 3,\n                                            value: formData.description_en,\n                                            onChange: (e)=>handleInputChange('description_en', e.target.value),\n                                            className: \"mt-1 block w-full rounded-md border-secondary-300 shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                            lineNumber: 645,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                    lineNumber: 641,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                            lineNumber: 629,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 gap-6 sm:grid-cols-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-secondary-700\",\n                                            children: \"İkon\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                            lineNumber: 655,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: formData.icon,\n                                            onChange: (e)=>handleInputChange('icon', e.target.value),\n                                            className: \"mt-1 block w-full rounded-md border-secondary-300 shadow-sm focus:border-primary-500 focus:ring-primary-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"atom\",\n                                                    children: \"Atom\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                                    lineNumber: 663,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"zap\",\n                                                    children: \"Zap\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                                    lineNumber: 664,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"beaker\",\n                                                    children: \"Beaker\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                                    lineNumber: 665,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"microscope\",\n                                                    children: \"Microscope\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                                    lineNumber: 666,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"code\",\n                                                    children: \"Code\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                                    lineNumber: 667,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"chart\",\n                                                    children: \"Chart\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                                    lineNumber: 668,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                            lineNumber: 658,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                    lineNumber: 654,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: formData.featured,\n                                                onChange: (e)=>handleInputChange('featured', e.target.checked),\n                                                className: \"rounded border-secondary-300 text-primary-600 shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                                lineNumber: 673,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2 text-sm text-secondary-700\",\n                                                children: \"\\xd6ne \\xc7ıkan\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                                lineNumber: 679,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                        lineNumber: 672,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                    lineNumber: 671,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                            lineNumber: 653,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 gap-6 sm:grid-cols-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-secondary-700\",\n                                    children: \"Sıra\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                    lineNumber: 688,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"number\",\n                                    value: formData.sort_order,\n                                    onChange: (e)=>handleInputChange('sort_order', parseInt(e.target.value)),\n                                    className: \"mt-1 block w-full rounded-md border-secondary-300 shadow-sm focus:border-primary-500 focus:ring-primary-500\",\n                                    min: \"1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                    lineNumber: 691,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                            lineNumber: 687,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-secondary-700\",\n                                    children: \"Durum\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                    lineNumber: 700,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: formData.status,\n                                    onChange: (e)=>handleInputChange('status', e.target.value),\n                                    className: \"mt-1 block w-full rounded-md border-secondary-300 shadow-sm focus:border-primary-500 focus:ring-primary-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"active\",\n                                            children: \"Aktif\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                            lineNumber: 708,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"inactive\",\n                                            children: \"Pasif\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                            lineNumber: 709,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                                    lineNumber: 703,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                            lineNumber: 699,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                    lineNumber: 686,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-end space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                            type: \"button\",\n                            onClick: onClose,\n                            className: \"bg-secondary-600 hover:bg-secondary-500\",\n                            children: \"İptal\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                            lineNumber: 715,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                            type: \"submit\",\n                            className: \"bg-primary-600 hover:bg-primary-500\",\n                            children: \"Kaydet\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                            lineNumber: 722,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n                    lineNumber: 714,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n            lineNumber: 537,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\app\\\\admin\\\\homepage\\\\page.tsx\",\n        lineNumber: 536,\n        columnNumber: 5\n    }, this);\n}\n_s1(EditModal, \"2m1sTIxajtVtwMGX3kErQtShduQ=\");\n_c4 = EditModal;\nvar _c, _c1, _c2, _c3, _c4;\n$RefreshReg$(_c, \"HomepagePage\");\n$RefreshReg$(_c1, \"HomepageContent\");\n$RefreshReg$(_c2, \"HeroSlidesTable\");\n$RefreshReg$(_c3, \"ServicesTable\");\n$RefreshReg$(_c4, \"EditModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/homepage/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/Modal.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/Modal.tsx ***!
  \*************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {



;
    // Wrapped in an IIFE to avoid polluting the global scope
    ;
    (function () {
        var _a, _b;
        // Legacy CSS implementations will `eval` browser code in a Node.js context
        // to extract CSS. For backwards compatibility, we need to check we're in a
        // browser context before continuing.
        if (typeof self !== 'undefined' &&
            // AMP / No-JS mode does not inject these helpers:
            '$RefreshHelpers$' in self) {
            // @ts-ignore __webpack_module__ is global
            var currentExports = module.exports;
            // @ts-ignore __webpack_module__ is global
            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;
            // This cannot happen in MainTemplate because the exports mismatch between
            // templating and execution.
            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);
            // A module can be accepted automatically based on its exports, e.g. when
            // it is a Refresh Boundary.
            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {
                // Save the previous exports signature on update so we can compare the boundary
                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)
                module.hot.dispose(function (data) {
                    data.prevSignature =
                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);
                });
                // Unconditionally accept an update to this module, we'll check if it's
                // still a Refresh Boundary later.
                // @ts-ignore importMeta is replaced in the loader
                module.hot.accept();
                // This field is set when the previous version of this module was a
                // Refresh Boundary, letting us know we need to check for invalidation or
                // enqueue an update.
                if (prevSignature !== null) {
                    // A boundary can become ineligible if its exports are incompatible
                    // with the previous exports.
                    //
                    // For example, if you add/remove/change exports, we'll want to
                    // re-execute the importing modules, and force those components to
                    // re-render. Similarly, if you convert a class component to a
                    // function, we want to invalidate the boundary.
                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {
                        module.hot.invalidate();
                    }
                    else {
                        self.$RefreshHelpers$.scheduleUpdate();
                    }
                }
            }
            else {
                // Since we just executed the code for the module, it's possible that the
                // new exports made it ineligible for being a boundary.
                // We only care about the case when we were _previously_ a boundary,
                // because we already accepted this update (accidental side effect).
                var isNoLongerABoundary = prevSignature !== null;
                if (isNoLongerABoundary) {
                    module.hot.invalidate();
                }
            }
        }
    })();


/***/ })

});