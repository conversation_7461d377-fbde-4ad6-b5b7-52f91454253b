<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

require_once __DIR__ . '/admin/config/database.php';

function sanitizeInput($input) {
    return htmlspecialchars(strip_tags(trim($input)));
}

try {
    $db = new Database();
    $method = $_SERVER['REQUEST_METHOD'];

    switch ($method) {
        case 'GET':
            // Get all contact subjects
            $language = $_GET['language'] ?? 'tr';
            $sql = "SELECT id,
                           " . ($language === 'en' ? 'COALESCE(name_en, name_tr)' : 'name_tr') . " as name,
                           name_tr, name_en, status, sort_order
                    FROM contact_subjects
                    WHERE status = 'active'
                    ORDER BY sort_order, name_tr";
            $subjects = $db->fetchAll($sql);
            
            echo json_encode([
                'success' => true,
                'subjects' => $subjects
            ]);
            break;

        case 'POST':
            // Create new subject
            $name_tr = sanitizeInput($_POST['name_tr'] ?? '');
            $name_en = sanitizeInput($_POST['name_en'] ?? '');
            $sort_order = (int)($_POST['sort_order'] ?? 0);
            
            if (empty($name_tr)) {
                echo json_encode(['success' => false, 'error' => 'Konu adı gerekli']);
                exit;
            }
            
            $sql = "INSERT INTO contact_subjects (name_tr, name_en, sort_order)
                    VALUES (:name_tr, :name_en, :sort_order)";
            
            $params = [
                'name_tr' => $name_tr,
                'name_en' => $name_en,
                'sort_order' => $sort_order
            ];
            
            if ($db->query($sql, $params)) {
                echo json_encode(['success' => true, 'message' => 'Konu başarıyla eklendi']);
            } else {
                echo json_encode(['success' => false, 'error' => 'Konu eklenirken hata oluştu']);
            }
            break;

        case 'PUT':
            // Update subject
            parse_str(file_get_contents("php://input"), $data);
            
            $id = (int)($data['id'] ?? 0);
            $name_tr = sanitizeInput($data['name_tr'] ?? '');
            $name_en = sanitizeInput($data['name_en'] ?? '');
            $sort_order = (int)($data['sort_order'] ?? 0);
            
            if (!$id || empty($name_tr)) {
                echo json_encode(['success' => false, 'error' => 'ID ve konu adı gerekli']);
                exit;
            }
            
            $sql = "UPDATE contact_subjects SET
                    name_tr = :name_tr,
                    name_en = :name_en, 
                    sort_order = :sort_order,
                    updated_at = CURRENT_TIMESTAMP
                    WHERE id = :id";
            
            $params = [
                'id' => $id,
                'name_tr' => $name_tr,
                'name_en' => $name_en,
                'sort_order' => $sort_order
            ];
            
            if ($db->query($sql, $params)) {
                echo json_encode(['success' => true, 'message' => 'Konu başarıyla güncellendi']);
            } else {
                echo json_encode(['success' => false, 'error' => 'Konu güncellenirken hata oluştu']);
            }
            break;

        case 'DELETE':
            // Delete subject
            parse_str(file_get_contents("php://input"), $data);
            $id = (int)($data['id'] ?? 0);
            
            if (!$id) {
                echo json_encode(['success' => false, 'error' => 'ID gerekli']);
                exit;
            }
            
            // Check if subject is used in contacts
            $used = $db->fetch("SELECT COUNT(*) as count FROM contacts WHERE subject = (SELECT name_tr FROM contact_subjects WHERE id = :id)", ['id' => $id]);
            
            if ($used['count'] > 0) {
                echo json_encode(['success' => false, 'error' => 'Bu konu iletişim taleplerinde kullanılıyor, silinemez']);
                exit;
            }
            
            $sql = "UPDATE contact_subjects SET status = 'inactive' WHERE id = :id";
            
            if ($db->query($sql, ['id' => $id])) {
                echo json_encode(['success' => true, 'message' => 'Konu başarıyla silindi']);
            } else {
                echo json_encode(['success' => false, 'error' => 'Konu silinirken hata oluştu']);
            }
            break;

        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            break;
    }

} catch (Exception $e) {
    error_log("Contact subjects API error: " . $e->getMessage());
    echo json_encode(['success' => false, 'error' => 'Veritabanı hatası']);
}
