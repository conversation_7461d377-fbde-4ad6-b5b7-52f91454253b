<?php
require_once 'config/database.php';
require_once 'config/auth.php';

// CORS headers
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Content-Type: application/json');

// <PERSON>le preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Check authentication
if (!isAuthenticated()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit();
}

$db = new Database();
$method = $_SERVER['REQUEST_METHOD'];

try {
    switch ($method) {
        case 'GET':
            if (isset($_GET['id'])) {
                // Get single page by ID
                $id = intval($_GET['id']);
                $page = $db->fetchOne("SELECT * FROM pages WHERE id = ?", [$id]);

                if (!$page) {
                    http_response_code(404);
                    echo json_encode(['error' => 'Page not found']);
                    exit();
                }

                echo json_encode(['success' => true, 'data' => $page]);
            } elseif (isset($_GET['slug'])) {
                // Get single page by slug
                $slug = $_GET['slug'];
                $page = $db->fetchOne("SELECT * FROM pages WHERE slug = ?", [$slug]);

                if (!$page) {
                    http_response_code(404);
                    echo json_encode(['error' => 'Page not found']);
                    exit();
                }

                echo json_encode(['success' => true, 'data' => $page]);
            } else {
                // Get all pages with pagination
                $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
                $limit = isset($_GET['limit']) ? min(100, max(1, intval($_GET['limit']))) : 20;
                $offset = ($page - 1) * $limit;
                
                // Search functionality
                $search = isset($_GET['search']) ? trim($_GET['search']) : '';
                $whereClause = '';
                $params = [];
                
                if (!empty($search)) {
                    $whereClause = "WHERE title_tr LIKE ? OR title_en LIKE ? OR content_tr LIKE ? OR content_en LIKE ?";
                    $searchParam = "%{$search}%";
                    $params = [$searchParam, $searchParam, $searchParam, $searchParam];
                }
                
                // Get total count
                $totalQuery = "SELECT COUNT(*) as total FROM pages " . $whereClause;
                $totalResult = $db->fetchOne($totalQuery, $params);
                $total = $totalResult['total'];
                
                // Get pages
                $pagesQuery = "SELECT * FROM pages " . $whereClause . " ORDER BY created_at DESC LIMIT ? OFFSET ?";
                $params[] = $limit;
                $params[] = $offset;
                $pages = $db->fetchAll($pagesQuery, $params);
                
                echo json_encode([
                    'success' => true,
                    'data' => $pages,
                    'pagination' => [
                        'page' => $page,
                        'limit' => $limit,
                        'total' => $total,
                        'pages' => ceil($total / $limit)
                    ]
                ]);
            }
            break;
            
        case 'POST':
            // Create new page
            $input = json_decode(file_get_contents('php://input'), true);
            
            $requiredFields = ['title_tr', 'title_en', 'content_tr', 'content_en', 'slug'];
            foreach ($requiredFields as $field) {
                if (!isset($input[$field]) || empty(trim($input[$field]))) {
                    http_response_code(400);
                    echo json_encode(['error' => "Field '{$field}' is required"]);
                    exit();
                }
            }
            
            // Check if slug already exists
            $existingPage = $db->fetchOne("SELECT id FROM pages WHERE slug = ?", [$input['slug']]);
            if ($existingPage) {
                http_response_code(400);
                echo json_encode(['error' => 'Slug already exists']);
                exit();
            }
            
            $result = $db->execute(
                "INSERT INTO pages (title_tr, title_en, content_tr, content_en, meta_description_tr, meta_description_en, slug, status, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())",
                [
                    $input['title_tr'],
                    $input['title_en'],
                    $input['content_tr'],
                    $input['content_en'],
                    $input['meta_description_tr'] ?? '',
                    $input['meta_description_en'] ?? '',
                    $input['slug'],
                    $input['status'] ?? 'draft'
                ]
            );
            
            if ($result) {
                $pageId = $db->getLastInsertId();
                echo json_encode([
                    'success' => true,
                    'message' => 'Page created successfully',
                    'data' => [
                        'id' => $pageId,
                        'slug' => $input['slug']
                    ]
                ]);
            } else {
                http_response_code(500);
                echo json_encode(['success' => false, 'message' => 'Failed to create page']);
            }
            break;
            
        case 'PUT':
            // Update page
            if (!isset($_GET['id'])) {
                http_response_code(400);
                echo json_encode(['error' => 'Page ID is required']);
                exit();
            }
            
            $id = intval($_GET['id']);
            $input = json_decode(file_get_contents('php://input'), true);
            
            $requiredFields = ['title_tr', 'title_en', 'content_tr', 'content_en', 'slug'];
            foreach ($requiredFields as $field) {
                if (!isset($input[$field]) || empty(trim($input[$field]))) {
                    http_response_code(400);
                    echo json_encode(['error' => "Field '{$field}' is required"]);
                    exit();
                }
            }
            
            // Check if slug already exists (excluding current page)
            $existingPage = $db->fetchOne("SELECT id FROM pages WHERE slug = ? AND id != ?", [$input['slug'], $id]);
            if ($existingPage) {
                http_response_code(400);
                echo json_encode(['error' => 'Slug already exists']);
                exit();
            }
            
            $result = $db->execute(
                "UPDATE pages SET title_tr = ?, title_en = ?, content_tr = ?, content_en = ?, meta_description_tr = ?, meta_description_en = ?, slug = ?, status = ?, updated_at = NOW() WHERE id = ?",
                [
                    $input['title_tr'],
                    $input['title_en'],
                    $input['content_tr'],
                    $input['content_en'],
                    $input['meta_description_tr'] ?? '',
                    $input['meta_description_en'] ?? '',
                    $input['slug'],
                    $input['status'] ?? 'draft',
                    $id
                ]
            );
            
            if ($result) {
                echo json_encode(['success' => true, 'message' => 'Page updated successfully']);
            } else {
                http_response_code(500);
                echo json_encode(['error' => 'Failed to update page']);
            }
            break;
            
        case 'DELETE':
            // Delete page
            if (!isset($_GET['id'])) {
                http_response_code(400);
                echo json_encode(['error' => 'Page ID is required']);
                exit();
            }
            
            $id = intval($_GET['id']);
            $result = $db->execute("DELETE FROM pages WHERE id = ?", [$id]);
            
            if ($result) {
                echo json_encode(['success' => true, 'message' => 'Page deleted successfully']);
            } else {
                http_response_code(500);
                echo json_encode(['error' => 'Failed to delete page']);
            }
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            break;
    }
} catch (Exception $e) {
    error_log("Pages API Error: " . $e->getMessage());
    error_log("Pages API stack trace: " . $e->getTraceAsString());
    http_response_code(500);
    echo json_encode(['error' => 'Internal server error: ' . $e->getMessage()]);
}
?>
