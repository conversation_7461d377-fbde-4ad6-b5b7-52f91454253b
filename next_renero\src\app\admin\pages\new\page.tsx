'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import WithAuth from '@/components/admin/auth/WithAuth';
import AdminLayout from '@/components/admin/layout/AdminLayout';
import { fetchFromAdminApi } from '@/utils/api';
import toast from 'react-hot-toast';
import { Button } from '@/components/ui/Button';
import { ArrowLeftIcon } from '@heroicons/react/24/outline';

interface NewPage {
  slug: string;
  title_tr: string;
  title_en: string;
  content_tr: string;
  content_en: string;
  meta_description_tr: string;
  meta_description_en: string;
  status: string;
}

export default function NewPagePage() {
  return (
    <WithAuth>
      <NewPageContent />
    </WithAuth>
  );
}

function NewPageContent() {
  const router = useRouter();
  
  const [page, setPage] = useState<NewPage>({
    slug: '',
    title_tr: '',
    title_en: '',
    content_tr: '',
    content_en: '',
    meta_description_tr: '',
    meta_description_en: '',
    status: 'active'
  });
  const [saving, setSaving] = useState(false);

  const handleInputChange = (field: keyof NewPage, value: string) => {
    setPage(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSave = async () => {
    if (!page.title_tr.trim()) {
      toast.error('Sayfa başlığı gereklidir');
      return;
    }

    if (!page.slug.trim()) {
      toast.error('URL slug gereklidir');
      return;
    }

    setSaving(true);
    try {
      const response = await fetchFromAdminApi('/pages.php', {
        method: 'POST',
        body: JSON.stringify(page)
      });

      if (response.success) {
        toast.success('Sayfa başarıyla oluşturuldu');
        router.push(`/admin/pages/${page.slug}`);
      } else {
        toast.error(response.message || 'Sayfa oluşturulurken hata oluştu');
      }
    } catch (error) {
      console.error('Kaydetme hatası:', error);
      toast.error('Kaydetme sırasında hata oluştu');
    } finally {
      setSaving(false);
    }
  };

  const generateSlug = (title: string) => {
    return title
      .toLowerCase()
      .replace(/ğ/g, 'g')
      .replace(/ü/g, 'u')
      .replace(/ş/g, 's')
      .replace(/ı/g, 'i')
      .replace(/ö/g, 'o')
      .replace(/ç/g, 'c')
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim('-');
  };

  const handleTitleChange = (value: string) => {
    handleInputChange('title_tr', value);
    if (!page.slug) {
      handleInputChange('slug', generateSlug(value));
    }
  };

  return (
    <AdminLayout>
      <div className="px-4 sm:px-6 lg:px-8 py-8">
        <div className="sm:flex sm:items-center">
          <div className="sm:flex-auto">
            <div className="flex items-center space-x-3">
              <button
                onClick={() => router.back()}
                className="p-2 text-secondary-400 hover:text-secondary-600"
              >
                <ArrowLeftIcon className="h-5 w-5" />
              </button>
              <div>
                <h1 className="text-xl font-semibold text-secondary-900">
                  Yeni Sayfa Oluştur
                </h1>
                <p className="mt-2 text-sm text-secondary-700">
                  Sitenize yeni bir sayfa ekleyin
                </p>
              </div>
            </div>
          </div>
          <div className="mt-4 sm:ml-16 sm:mt-0 sm:flex-none">
            <Button
              onClick={handleSave}
              disabled={saving}
              className="bg-primary-600 hover:bg-primary-500"
            >
              {saving ? 'Oluşturuluyor...' : 'Sayfa Oluştur'}
            </Button>
          </div>
        </div>

        <div className="mt-8 space-y-8">
          {/* Temel Bilgiler */}
          <div className="bg-white shadow rounded-lg p-6">
            <h2 className="text-lg font-medium text-secondary-900 mb-4">Temel Bilgiler</h2>
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
              <div>
                <label className="block text-sm font-medium text-secondary-700">
                  Sayfa Başlığı (Türkçe) *
                </label>
                <input
                  type="text"
                  value={page.title_tr}
                  onChange={(e) => handleTitleChange(e.target.value)}
                  className="mt-1 block w-full rounded-md border-secondary-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                  placeholder="Örn: Hakkımızda"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-secondary-700">
                  Sayfa Başlığı (İngilizce)
                </label>
                <input
                  type="text"
                  value={page.title_en}
                  onChange={(e) => handleInputChange('title_en', e.target.value)}
                  className="mt-1 block w-full rounded-md border-secondary-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                  placeholder="e.g: About Us"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-secondary-700">
                  URL Slug *
                </label>
                <input
                  type="text"
                  value={page.slug}
                  onChange={(e) => handleInputChange('slug', e.target.value)}
                  className="mt-1 block w-full rounded-md border-secondary-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                  placeholder="hakkimizda"
                  required
                />
                <p className="mt-1 text-sm text-secondary-500">
                  Sayfanın URL'inde görünecek kısım. Sadece küçük harf, rakam ve tire kullanın.
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-secondary-700">
                  Durum
                </label>
                <select
                  value={page.status}
                  onChange={(e) => handleInputChange('status', e.target.value)}
                  className="mt-1 block w-full rounded-md border-secondary-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                >
                  <option value="active">Aktif</option>
                  <option value="inactive">Pasif</option>
                </select>
              </div>
            </div>
          </div>

          {/* İçerik */}
          <div className="bg-white shadow rounded-lg p-6">
            <h2 className="text-lg font-medium text-secondary-900 mb-4">Sayfa İçeriği</h2>
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-secondary-700">
                  İçerik (Türkçe)
                </label>
                <textarea
                  rows={12}
                  value={page.content_tr}
                  onChange={(e) => handleInputChange('content_tr', e.target.value)}
                  className="mt-1 block w-full rounded-md border-secondary-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                  placeholder="Sayfa içeriğinizi buraya yazın. HTML etiketleri kullanabilirsiniz."
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-secondary-700">
                  İçerik (İngilizce)
                </label>
                <textarea
                  rows={12}
                  value={page.content_en}
                  onChange={(e) => handleInputChange('content_en', e.target.value)}
                  className="mt-1 block w-full rounded-md border-secondary-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                  placeholder="Write your page content here. You can use HTML tags."
                />
              </div>
            </div>
          </div>

          {/* SEO Ayarları */}
          <div className="bg-white shadow rounded-lg p-6">
            <h2 className="text-lg font-medium text-secondary-900 mb-4">SEO Ayarları</h2>
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
              <div>
                <label className="block text-sm font-medium text-secondary-700">
                  Meta Açıklama (Türkçe)
                </label>
                <textarea
                  rows={3}
                  value={page.meta_description_tr}
                  onChange={(e) => handleInputChange('meta_description_tr', e.target.value)}
                  className="mt-1 block w-full rounded-md border-secondary-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                  placeholder="Sayfanızın kısa açıklaması (arama motorları için)"
                  maxLength={160}
                />
                <p className="mt-1 text-sm text-secondary-500">
                  {page.meta_description_tr.length}/160 karakter
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-secondary-700">
                  Meta Açıklama (İngilizce)
                </label>
                <textarea
                  rows={3}
                  value={page.meta_description_en}
                  onChange={(e) => handleInputChange('meta_description_en', e.target.value)}
                  className="mt-1 block w-full rounded-md border-secondary-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                  placeholder="Short description of your page (for search engines)"
                  maxLength={160}
                />
                <p className="mt-1 text-sm text-secondary-500">
                  {page.meta_description_en.length}/160 characters
                </p>
              </div>
            </div>
          </div>

          {/* Yardım */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
            <h3 className="text-lg font-medium text-blue-900 mb-2">💡 İpuçları</h3>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• Sayfa başlığı SEO için önemlidir, açıklayıcı olmasına dikkat edin</li>
              <li>• URL slug benzersiz olmalı ve Türkçe karakter içermemelidir</li>
              <li>• Meta açıklama arama motorlarında görüntülenir, çekici yazın</li>
              <li>• İçerikte HTML etiketleri kullanabilirsiniz (&lt;h2&gt;, &lt;p&gt;, &lt;strong&gt; vb.)</li>
            </ul>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}
