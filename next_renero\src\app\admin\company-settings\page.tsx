'use client';

import { useState, useEffect } from 'react';
import WithAuth from '@/components/admin/auth/WithAuth';
import AdminLayout from '@/components/admin/layout/AdminLayout';
import { fetchFromAdminApi } from '@/utils/api';
import toast from 'react-hot-toast';
import { Button } from '@/components/ui/Button';
import { PhotoIcon } from '@heroicons/react/24/outline';

interface CompanySettings {
  site_title: string;
  site_description: string;
  company_name: string;
  company_subtitle_tr: string;
  company_subtitle_en: string;
  contact_email: string;
  contact_phone: string;
  company_address: string;
  social_linkedin: string;
  social_twitter: string;
  social_facebook: string;
  social_instagram: string;
  social_youtube: string;
  admin_panel_name: string;
  site_logo: string;
}

export default function CompanySettingsPage() {
  return (
    <WithAuth>
      <CompanySettingsContent />
    </WithAuth>
  );
}

function CompanySettingsContent() {
  const [settings, setSettings] = useState<CompanySettings>({
    site_title: '',
    site_description: '',
    company_name: '',
    company_subtitle_tr: '',
    company_subtitle_en: '',
    contact_email: '',
    contact_phone: '',
    company_address: '',
    social_linkedin: '',
    social_twitter: '',
    social_facebook: '',
    social_instagram: '',
    social_youtube: '',
    admin_panel_name: '',
    site_logo: '',
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    try {
      const response = await fetchFromAdminApi('/settings.php');
      if (response.success && response.data) {
        const settingsData: CompanySettings = {
          site_title: '',
          site_description: '',
          company_name: '',
          company_subtitle_tr: '',
          company_subtitle_en: '',
          contact_email: '',
          contact_phone: '',
          company_address: '',
          social_linkedin: '',
          social_twitter: '',
          social_facebook: '',
          social_instagram: '',
          social_youtube: '',
          admin_panel_name: '',
          site_logo: '',
        };

        // API'den gelen verileri settings objesine dönüştür
        response.data.forEach((item: any) => {
          if (item.setting_key in settingsData) {
            (settingsData as any)[item.setting_key] = item.setting_value || '';
          }
        });

        setSettings(settingsData);
      }
    } catch (error) {
      console.error('Ayarlar yüklenirken hata:', error);
      toast.error('Ayarlar yüklenirken hata oluştu');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (key: keyof CompanySettings, value: string) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleSave = async () => {
    setSaving(true);
    try {
      const response = await fetchFromAdminApi('/settings.php', {
        method: 'POST',
        body: JSON.stringify(settings)
      });

      if (response.success) {
        toast.success('Şirket bilgileri başarıyla güncellendi');
      } else {
        toast.error(response.message || 'Güncelleme sırasında hata oluştu');
      }
    } catch (error) {
      console.error('Kaydetme hatası:', error);
      toast.error('Kaydetme sırasında hata oluştu');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="px-4 sm:px-6 lg:px-8 py-8">
        <div className="sm:flex sm:items-center">
          <div className="sm:flex-auto">
            <h1 className="text-xl font-semibold text-secondary-900">Şirket Bilgileri</h1>
            <p className="mt-2 text-sm text-secondary-700">
              Şirket bilgilerini, logoyu ve sosyal medya hesaplarını buradan yönetebilirsiniz.
            </p>
          </div>
          <div className="mt-4 sm:ml-16 sm:mt-0 sm:flex-none">
            <Button
              onClick={handleSave}
              disabled={saving}
              className="bg-primary-600 hover:bg-primary-500"
            >
              {saving ? 'Kaydediliyor...' : 'Kaydet'}
            </Button>
          </div>
        </div>

        <div className="mt-8 space-y-8">
          {/* Genel Bilgiler */}
          <div className="bg-white shadow rounded-lg p-6">
            <h2 className="text-lg font-medium text-secondary-900 mb-4">Genel Bilgiler</h2>
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
              <div>
                <label className="block text-sm font-medium text-secondary-700">
                  Site Başlığı
                </label>
                <input
                  type="text"
                  value={settings.site_title}
                  onChange={(e) => handleInputChange('site_title', e.target.value)}
                  className="mt-1 block w-full rounded-md border-secondary-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-secondary-700">
                  Şirket Adı
                </label>
                <input
                  type="text"
                  value={settings.company_name}
                  onChange={(e) => handleInputChange('company_name', e.target.value)}
                  className="mt-1 block w-full rounded-md border-secondary-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                />
              </div>
              <div className="sm:col-span-2">
                <label className="block text-sm font-medium text-secondary-700">
                  Site Açıklaması
                </label>
                <textarea
                  rows={3}
                  value={settings.site_description}
                  onChange={(e) => handleInputChange('site_description', e.target.value)}
                  className="mt-1 block w-full rounded-md border-secondary-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                />
              </div>
            </div>
          </div>

          {/* Logo */}
          <div className="bg-white shadow rounded-lg p-6">
            <h2 className="text-lg font-medium text-secondary-900 mb-4">Logo</h2>
            <div className="flex items-center space-x-6">
              <div className="shrink-0">
                {settings.site_logo ? (
                  <img
                    className="h-16 w-16 object-cover rounded-lg"
                    src={settings.site_logo}
                    alt="Logo"
                  />
                ) : (
                  <div className="h-16 w-16 bg-secondary-100 rounded-lg flex items-center justify-center">
                    <PhotoIcon className="h-8 w-8 text-secondary-400" />
                  </div>
                )}
              </div>
              <div className="flex-1">
                <label className="block text-sm font-medium text-secondary-700">
                  Logo URL
                </label>
                <input
                  type="url"
                  value={settings.site_logo}
                  onChange={(e) => handleInputChange('site_logo', e.target.value)}
                  placeholder="https://example.com/logo.png"
                  className="mt-1 block w-full rounded-md border-secondary-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                />
              </div>
            </div>
          </div>

          {/* İletişim Bilgileri */}
          <div className="bg-white shadow rounded-lg p-6">
            <h2 className="text-lg font-medium text-secondary-900 mb-4">İletişim Bilgileri</h2>
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
              <div>
                <label className="block text-sm font-medium text-secondary-700">
                  E-posta
                </label>
                <input
                  type="email"
                  value={settings.contact_email}
                  onChange={(e) => handleInputChange('contact_email', e.target.value)}
                  className="mt-1 block w-full rounded-md border-secondary-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-secondary-700">
                  Telefon
                </label>
                <input
                  type="tel"
                  value={settings.contact_phone}
                  onChange={(e) => handleInputChange('contact_phone', e.target.value)}
                  className="mt-1 block w-full rounded-md border-secondary-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                />
              </div>
              <div className="sm:col-span-2">
                <label className="block text-sm font-medium text-secondary-700">
                  Adres
                </label>
                <textarea
                  rows={2}
                  value={settings.company_address}
                  onChange={(e) => handleInputChange('company_address', e.target.value)}
                  className="mt-1 block w-full rounded-md border-secondary-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                />
              </div>
            </div>
          </div>

          {/* Sosyal Medya */}
          <div className="bg-white shadow rounded-lg p-6">
            <h2 className="text-lg font-medium text-secondary-900 mb-4">Sosyal Medya</h2>
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
              <div>
                <label className="block text-sm font-medium text-secondary-700">
                  LinkedIn
                </label>
                <input
                  type="url"
                  value={settings.social_linkedin}
                  onChange={(e) => handleInputChange('social_linkedin', e.target.value)}
                  placeholder="https://linkedin.com/company/..."
                  className="mt-1 block w-full rounded-md border-secondary-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-secondary-700">
                  Twitter
                </label>
                <input
                  type="url"
                  value={settings.social_twitter}
                  onChange={(e) => handleInputChange('social_twitter', e.target.value)}
                  placeholder="https://twitter.com/..."
                  className="mt-1 block w-full rounded-md border-secondary-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-secondary-700">
                  Facebook
                </label>
                <input
                  type="url"
                  value={settings.social_facebook}
                  onChange={(e) => handleInputChange('social_facebook', e.target.value)}
                  placeholder="https://facebook.com/..."
                  className="mt-1 block w-full rounded-md border-secondary-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-secondary-700">
                  Instagram
                </label>
                <input
                  type="url"
                  value={settings.social_instagram}
                  onChange={(e) => handleInputChange('social_instagram', e.target.value)}
                  placeholder="https://instagram.com/..."
                  className="mt-1 block w-full rounded-md border-secondary-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-secondary-700">
                  YouTube
                </label>
                <input
                  type="url"
                  value={settings.social_youtube}
                  onChange={(e) => handleInputChange('social_youtube', e.target.value)}
                  placeholder="https://youtube.com/..."
                  className="mt-1 block w-full rounded-md border-secondary-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}
