# RENERO Geliştirme Ortamı Kurulumu

Bu dokümanda RENERO projesini localhost'ta geliştirme ortamında çalıştırmak için gerekli adımlar açıklanmıştır.

## Gereksinimler

- PHP 7.4 veya üzeri
- Node.js 18 veya üzeri
- Internet bağlantısı (uzak veritabanı için)

## Kurulum Adımları

### 1. PHP API Sunucusunu Başlatma

Proje kök dizininde terminal açın ve aşağıdaki komutu çalıştırın:

```bash
# Windows için
start-api-server.bat

# veya manuel olarak
php -S localhost:8080 -t api/
```

Bu komut PHP'nin built-in sunucusunu başlatır ve API'leri `http://localhost:8080` adresinde erişilebilir hale getirir.

### 2. Next.js Geliştirme Sunucusunu Başlatma

Yeni bir terminal açın ve `next_renero` klas<PERSON>r<PERSON>ne gidin:

```bash
cd next_renero
npm run dev
```

Bu komut Next.js geliştirme sunucusunu `http://localhost:3000` adresinde başlatır.

## API Endpoint'leri

### Geliştirme Ortamında API URL'leri:

- **Ana API**: `http://localhost:8080/`
- **Admin API**: `http://localhost:8080/admin/`

### Mevcut Admin API Endpoint'leri:

- `GET /admin/settings.php` - Şirket ayarlarını getir
- `POST /admin/settings.php` - Şirket ayarlarını güncelle
- `GET /admin/pages.php` - Sayfaları listele
- `GET /admin/pages.php?slug=sayfa-slug` - Belirli sayfayı getir
- `POST /admin/pages.php` - Yeni sayfa oluştur
- `PUT /admin/pages.php?id=1` - Sayfayı güncelle
- `DELETE /admin/pages.php?id=1` - Sayfayı sil
- `GET /admin/hero-slides.php` - Hero slider'ları getir
- `POST /admin/hero-slides.php` - Yeni slider ekle
- `GET /admin/services.php` - Hizmetleri getir
- `POST /admin/services.php` - Yeni hizmet ekle

## Veritabanı Bağlantısı

Geliştirme ortamında API'ler doğrudan uzak veritabanına bağlanır:

- **Host**: **************
- **Database**: renero_db
- **Username**: renero_user
- **Password**: Renero2025!

## Admin Panel Giriş Bilgileri

Admin paneline giriş yapmak için:

- **URL**: `http://localhost:3000/admin/login`
- **Kullanıcı Adı**: Veritabanındaki admin kullanıcısı
- **Şifre**: Veritabanındaki admin şifresi

## Yeni Özellikler

### 1. Yeniden Yapılandırılmış Sidebar

- ✅ Kullanıcılar sekmesi kaldırıldı
- ✅ Sayfalar açılır menü sistemi eklendi
- ✅ Şirket Bilgileri sekmesi eklendi
- ✅ Ana Sayfa Slider → Ana Sayfa altına taşındı

### 2. Şirket Bilgileri Yönetimi (`/admin/company-settings`)

- Site başlığı ve açıklaması
- Şirket adı ve logo
- İletişim bilgileri
- Sosyal medya hesapları

### 3. Ana Sayfa Yönetimi (`/admin/homepage`)

- Hero Slider yönetimi
- Hizmetler bölümü yönetimi
- Sıralama ve durum kontrolü

### 4. Dinamik Sayfa Sistemi

- Yeni sayfa ekleme (`/admin/pages/new`)
- Sayfa düzenleme (`/admin/pages/[slug]`)
- Çoklu dil desteği (TR/EN)
- SEO ayarları

## Sorun Giderme

### API 404 Hataları

Eğer admin panelinde API 404 hataları alıyorsanız:

1. PHP sunucusunun çalıştığından emin olun (`http://localhost:8080`)
2. `start-api-server.bat` dosyasını çalıştırın
3. Browser'da `http://localhost:8080/admin/settings.php` adresini test edin

### CORS Hataları

Eğer CORS hataları alıyorsanız:

1. PHP sunucusunu yeniden başlatın
2. Browser cache'ini temizleyin
3. API dosyalarında CORS header'larının doğru olduğundan emin olun

### Veritabanı Bağlantı Hataları

Eğer veritabanı bağlantı hataları alıyorsanız:

1. Internet bağlantınızı kontrol edin
2. `api/admin/config/database.php` dosyasındaki bağlantı bilgilerini kontrol edin
3. Sunucu IP'sinin erişilebilir olduğundan emin olun

## Geliştirme Notları

- API'ler gerçek veritabanına bağlanır, test verilerini dikkatli kullanın
- Geliştirme sırasında yapılan değişiklikler canlı veritabanını etkiler
- Production'a deploy etmeden önce tüm değişiklikleri test edin

## Deployment

Production'a deploy etmek için:

1. `build-deploy.js` scriptini kullanın
2. Veya manuel olarak `next_renero` klasöründe `npm run build` çalıştırın
3. Build çıktısını sunucuya yükleyin

## Destek

Herhangi bir sorun yaşarsanız:

1. Bu dokümandaki sorun giderme bölümünü kontrol edin
2. Console log'larını inceleyin
3. API endpoint'lerini browser'da manuel test edin
