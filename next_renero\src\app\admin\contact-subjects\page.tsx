'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { PlusIcon, PencilIcon, TrashIcon } from '@heroicons/react/24/outline';
import WithAuth from '@/components/admin/auth/WithAuth';
import AdminLayout from '@/components/admin/layout/AdminLayout';
import Modal from '@/components/ui/Modal';
import toast from 'react-hot-toast';

interface ContactSubject {
  id: number;
  name_tr: string;
  name_en: string;
  status: string;
  sort_order: number;
  created_at: string;
  updated_at: string;
}

interface FormData {
  name_tr: string;
  name_en: string;
  sort_order: number;
}

export default function ContactSubjectsPage() {
  const [subjects, setSubjects] = useState<ContactSubject[]>([]);
  const [loading, setLoading] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingSubject, setEditingSubject] = useState<ContactSubject | null>(null);
  const [formData, setFormData] = useState<FormData>({
    name_tr: '',
    name_en: '',
    sort_order: 0
  });

  // Fetch subjects
  const fetchSubjects = async () => {
    try {
      const { fetchFromAdminApi } = await import('@/utils/api');
      const response = await fetchFromAdminApi('/contact-subjects.php');
      
      if (response.success && response.data) {
        setSubjects(response.data);
      }
    } catch (error) {
      console.error('Error fetching subjects:', error);
      toast.error('Konular yüklenirken hata oluştu');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSubjects();
  }, []);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name_tr.trim()) {
      toast.error('Türkçe konu adı gereklidir');
      return;
    }

    try {
      const { fetchFromAdminApi } = await import('@/utils/api');
      
      if (editingSubject) {
        // Update existing subject
        const response = await fetchFromAdminApi('/contact-subjects.php', {
          method: 'PUT',
          body: JSON.stringify({
            id: editingSubject.id,
            ...formData
          })
        });
        
        if (response.success) {
          toast.success('Konu başarıyla güncellendi');
          fetchSubjects();
          closeModal();
        } else {
          toast.error(response.message || 'Güncelleme sırasında hata oluştu');
        }
      } else {
        // Create new subject
        const response = await fetchFromAdminApi('/contact-subjects.php', {
          method: 'POST',
          body: new URLSearchParams(formData as any)
        });
        
        if (response.success) {
          toast.success('Konu başarıyla eklendi');
          fetchSubjects();
          closeModal();
        } else {
          toast.error(response.message || 'Ekleme sırasında hata oluştu');
        }
      }
    } catch (error) {
      console.error('Error saving subject:', error);
      toast.error('Kaydetme sırasında hata oluştu');
    }
  };

  // Handle delete
  const handleDelete = async (id: number) => {
    if (!confirm('Bu konuyu silmek istediğinizden emin misiniz?')) {
      return;
    }

    try {
      const { fetchFromAdminApi } = await import('@/utils/api');
      const response = await fetchFromAdminApi('/contact-subjects.php', {
        method: 'DELETE',
        body: JSON.stringify({ id })
      });
      
      if (response.success) {
        toast.success('Konu başarıyla silindi');
        fetchSubjects();
      } else {
        toast.error(response.message || 'Silme sırasında hata oluştu');
      }
    } catch (error) {
      console.error('Error deleting subject:', error);
      toast.error('Silme sırasında hata oluştu');
    }
  };

  // Modal functions
  const openModal = (subject?: ContactSubject) => {
    if (subject) {
      setEditingSubject(subject);
      setFormData({
        name_tr: subject.name_tr,
        name_en: subject.name_en,
        sort_order: subject.sort_order
      });
    } else {
      setEditingSubject(null);
      setFormData({
        name_tr: '',
        name_en: '',
        sort_order: subjects.length + 1
      });
    }
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setEditingSubject(null);
    setFormData({
      name_tr: '',
      name_en: '',
      sort_order: 0
    });
  };

  return (
    <WithAuth>
      <AdminLayout>
        <div className="space-y-6">
          {/* Header */}
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">İletişim Konuları</h1>
              <p className="text-gray-600">İletişim formunda kullanılacak konuları yönetin</p>
            </div>
            <button
              onClick={() => openModal()}
              className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 flex items-center gap-2"
            >
              <PlusIcon className="w-5 h-5" />
              Yeni Konu Ekle
            </button>
          </div>

          {/* Content */}
          {loading ? (
            <div className="flex justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
            </div>
          ) : (
            <div className="bg-white rounded-lg shadow overflow-hidden">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Sıra
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Türkçe Adı
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      İngilizce Adı
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Durum
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      İşlemler
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {subjects.map((subject) => (
                    <tr key={subject.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {subject.sort_order}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {subject.name_tr}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {subject.name_en}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          subject.status === 'active' 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {subject.status === 'active' ? 'Aktif' : 'Pasif'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                        <button
                          onClick={() => openModal(subject)}
                          className="text-primary-600 hover:text-primary-900"
                        >
                          <PencilIcon className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => handleDelete(subject.id)}
                          className="text-red-600 hover:text-red-900"
                        >
                          <TrashIcon className="w-4 h-4" />
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>

        {/* Modal */}
        <Modal
          isOpen={isModalOpen}
          onClose={closeModal}
          title={editingSubject ? 'Konu Düzenle' : 'Yeni Konu Ekle'}
          size="md"
        >
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Türkçe Konu Adı *
              </label>
              <input
                type="text"
                value={formData.name_tr}
                onChange={(e) => setFormData({ ...formData, name_tr: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                required
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                İngilizce Konu Adı
              </label>
              <input
                type="text"
                value={formData.name_en}
                onChange={(e) => setFormData({ ...formData, name_en: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Sıra Numarası
              </label>
              <input
                type="number"
                value={formData.sort_order}
                onChange={(e) => setFormData({ ...formData, sort_order: parseInt(e.target.value) || 0 })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                min="1"
              />
            </div>
            
            <div className="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={closeModal}
                className="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300"
              >
                İptal
              </button>
              <button
                type="submit"
                className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700"
              >
                {editingSubject ? 'Güncelle' : 'Ekle'}
              </button>
            </div>
          </form>
        </Modal>
      </AdminLayout>
    </WithAuth>
  );
}
