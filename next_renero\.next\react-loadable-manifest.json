{"..\\node_modules\\next\\dist\\client\\dev\\hot-reloader\\app\\use-websocket.js -> @vercel/turbopack-ecmascript-runtime/browser/dev/hmr-client/hmr-client.ts": {"id": "..\\node_modules\\next\\dist\\client\\dev\\hot-reloader\\app\\use-websocket.js -> @vercel/turbopack-ecmascript-runtime/browser/dev/hmr-client/hmr-client.ts", "files": ["static/chunks/_app-pages-browser_node_modules_next_dist_client_dev_noop-turbopack-hmr_js.js"]}, "app\\admin\\contact-subjects\\page.tsx -> @/utils/api": {"id": "app\\admin\\contact-subjects\\page.tsx -> @/utils/api", "files": []}, "components\\layout\\Footer.tsx -> @/utils/api": {"id": "components\\layout\\Footer.tsx -> @/utils/api", "files": ["static/chunks/_app-pages-browser_src_utils_api_ts.js"]}, "components\\layout\\Header.tsx -> @/utils/api": {"id": "components\\layout\\Header.tsx -> @/utils/api", "files": ["static/chunks/_app-pages-browser_src_utils_api_ts.js"]}, "components\\sections\\Hero.tsx -> @/utils/api": {"id": "components\\sections\\Hero.tsx -> @/utils/api", "files": ["static/chunks/_app-pages-browser_src_utils_api_ts.js"]}, "components\\sections\\Services.tsx -> @/utils/api": {"id": "components\\sections\\Services.tsx -> @/utils/api", "files": ["static/chunks/_app-pages-browser_src_utils_api_ts.js"]}, "contexts\\SettingsContext.tsx -> @/utils/api": {"id": "contexts\\SettingsContext.tsx -> @/utils/api", "files": ["static/chunks/_app-pages-browser_src_utils_api_ts.js"]}}