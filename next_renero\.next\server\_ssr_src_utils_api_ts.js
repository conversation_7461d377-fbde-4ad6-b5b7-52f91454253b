"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_utils_api_ts";
exports.ids = ["_ssr_src_utils_api_ts"];
exports.modules = {

/***/ "(ssr)/./src/utils/api.ts":
/*!**************************!*\
  !*** ./src/utils/api.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fetchFromAdminApi: () => (/* binding */ fetchFromAdminApi),\n/* harmony export */   fetchFromApi: () => (/* binding */ fetchFromApi),\n/* harmony export */   getApiUrl: () => (/* binding */ getApiUrl)\n/* harmony export */ });\n/* __next_internal_client_entry_do_not_use__ getApiUrl,fetchFromApi,fetchFromAdminApi auto */ /**\n * API istekleri için yardımcı fonksiyon\n * @param endpoint - API endpoint yolu (örn: '/navigation.php')\n * @param params - URL parametreleri\n * @returns Tam API URL'si\n */ function getApiUrl(endpoint, params) {\n    // BASE_API_URL değerini çevre değişkeninden al - main API için\n    const baseApiUrl = \"http://localhost:3000/api\" || (0);\n    // Endpoint'i tam URL'ye dönüştür\n    let url = `${baseApiUrl}${endpoint}`;\n    // Parametreler varsa URL'ye ekle\n    if (params && Object.keys(params).length > 0) {\n        const queryString = Object.entries(params).map(([key, value])=>`${encodeURIComponent(key)}=${encodeURIComponent(value)}`).join('&');\n        url = `${url}?${queryString}`;\n    }\n    return url;\n}\n/**\n * API'den veri getirmek için yardımcı fonksiyon\n * @param endpoint - API endpoint yolu (örn: '/navigation.php')\n * @param params - URL parametreleri\n * @returns API yanıtı\n */ async function fetchFromApi(endpoint, params) {\n    // Mock veri sistemini devre dışı bırak - gerçek API kullan\n    console.log('Production mode: Using real API for', endpoint);\n    const url = getApiUrl(endpoint, params);\n    try {\n        const response = await fetch(url);\n        if (!response.ok) {\n            throw new Error(`API error: ${response.status}`);\n        }\n        const data = await response.json();\n        return data;\n    } catch (error) {\n        console.error('API fetch error:', error);\n        throw error;\n    }\n}\n/**\n * Admin API istekleri için yardımcı fonksiyon\n * @param endpoint - API endpoint yolu (örn: '/users.php')\n * @param options - Fetch options (method, body, headers)\n * @returns API yanıtı\n */ async function fetchFromAdminApi(endpoint, options) {\n    // Admin API base URL\n    const baseApiUrl =  false ? 0 : 'http://localhost:8080/admin';\n    const url = `${baseApiUrl}${endpoint}`;\n    console.log('Admin API request:', url);\n    try {\n        const response = await fetch(url, {\n            headers: {\n                'Content-Type': 'application/json',\n                ...options?.headers\n            },\n            ...options\n        });\n        if (!response.ok) {\n            throw new Error(`Admin API error: ${response.status}`);\n        }\n        const data = await response.json();\n        return data;\n    } catch (error) {\n        console.error('Admin API fetch error:', error);\n        throw error;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/api.ts\n");

/***/ })

};
;