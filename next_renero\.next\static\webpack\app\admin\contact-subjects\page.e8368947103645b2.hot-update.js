"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/contact-subjects/page",{

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction ChevronDownIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"m19.5 8.25-7.5 7.5-7.5-7.5\"\n    }));\n}\n_c = ChevronDownIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(ChevronDownIcon);\n_c1 = ForwardRef;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"ChevronDownIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/admin/layout/Sidebar.tsx":
/*!*************************************************!*\
  !*** ./src/components/admin/layout/Sidebar.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Dialog,Transition!=!@headlessui/react */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/components/transition/transition.js\");\n/* harmony import */ var _barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Dialog,Transition!=!@headlessui/react */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/components/dialog/dialog.js\");\n/* harmony import */ var _barrel_optimize_names_BuildingOfficeIcon_ChevronDownIcon_Cog6ToothIcon_DocumentIcon_DocumentTextIcon_EnvelopeIcon_HomeIcon_PlusIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BuildingOfficeIcon,ChevronDownIcon,Cog6ToothIcon,DocumentIcon,DocumentTextIcon,EnvelopeIcon,HomeIcon,PlusIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/HomeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BuildingOfficeIcon_ChevronDownIcon_Cog6ToothIcon_DocumentIcon_DocumentTextIcon_EnvelopeIcon_HomeIcon_PlusIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BuildingOfficeIcon,ChevronDownIcon,Cog6ToothIcon,DocumentIcon,DocumentTextIcon,EnvelopeIcon,HomeIcon,PlusIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EnvelopeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BuildingOfficeIcon_ChevronDownIcon_Cog6ToothIcon_DocumentIcon_DocumentTextIcon_EnvelopeIcon_HomeIcon_PlusIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BuildingOfficeIcon,ChevronDownIcon,Cog6ToothIcon,DocumentIcon,DocumentTextIcon,EnvelopeIcon,HomeIcon,PlusIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BuildingOfficeIcon_ChevronDownIcon_Cog6ToothIcon_DocumentIcon_DocumentTextIcon_EnvelopeIcon_HomeIcon_PlusIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BuildingOfficeIcon,ChevronDownIcon,Cog6ToothIcon,DocumentIcon,DocumentTextIcon,EnvelopeIcon,HomeIcon,PlusIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BuildingOfficeIcon_ChevronDownIcon_Cog6ToothIcon_DocumentIcon_DocumentTextIcon_EnvelopeIcon_HomeIcon_PlusIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BuildingOfficeIcon,ChevronDownIcon,Cog6ToothIcon,DocumentIcon,DocumentTextIcon,EnvelopeIcon,HomeIcon,PlusIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BuildingOfficeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BuildingOfficeIcon_ChevronDownIcon_Cog6ToothIcon_DocumentIcon_DocumentTextIcon_EnvelopeIcon_HomeIcon_PlusIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BuildingOfficeIcon,ChevronDownIcon,Cog6ToothIcon,DocumentIcon,DocumentTextIcon,EnvelopeIcon,HomeIcon,PlusIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/Cog6ToothIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BuildingOfficeIcon_ChevronDownIcon_Cog6ToothIcon_DocumentIcon_DocumentTextIcon_EnvelopeIcon_HomeIcon_PlusIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BuildingOfficeIcon,ChevronDownIcon,Cog6ToothIcon,DocumentIcon,DocumentTextIcon,EnvelopeIcon,HomeIcon,PlusIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BuildingOfficeIcon_ChevronDownIcon_Cog6ToothIcon_DocumentIcon_DocumentTextIcon_EnvelopeIcon_HomeIcon_PlusIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BuildingOfficeIcon,ChevronDownIcon,Cog6ToothIcon,DocumentIcon,DocumentTextIcon,EnvelopeIcon,HomeIcon,PlusIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BuildingOfficeIcon_ChevronDownIcon_Cog6ToothIcon_DocumentIcon_DocumentTextIcon_EnvelopeIcon_HomeIcon_PlusIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BuildingOfficeIcon,ChevronDownIcon,Cog6ToothIcon,DocumentIcon,DocumentTextIcon,EnvelopeIcon,HomeIcon,PlusIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _contexts_SettingsContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/SettingsContext */ \"(app-pages-browser)/./src/contexts/SettingsContext.tsx\");\n/* harmony import */ var _utils_api__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/api */ \"(app-pages-browser)/./src/utils/api.ts\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! clsx */ \"(app-pages-browser)/./node_modules/clsx/dist/clsx.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nconst navigation = [\n    {\n        name: 'Kontrol Paneli',\n        href: '/admin/dashboard',\n        icon: _barrel_optimize_names_BuildingOfficeIcon_ChevronDownIcon_Cog6ToothIcon_DocumentIcon_DocumentTextIcon_EnvelopeIcon_HomeIcon_PlusIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n    },\n    {\n        name: 'İletişim Talepleri',\n        href: '/admin/contacts',\n        icon: _barrel_optimize_names_BuildingOfficeIcon_ChevronDownIcon_Cog6ToothIcon_DocumentIcon_DocumentTextIcon_EnvelopeIcon_HomeIcon_PlusIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n    },\n    {\n        name: 'Bloglar',\n        href: '/admin/blogs',\n        icon: _barrel_optimize_names_BuildingOfficeIcon_ChevronDownIcon_Cog6ToothIcon_DocumentIcon_DocumentTextIcon_EnvelopeIcon_HomeIcon_PlusIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n    },\n    {\n        name: 'Sayfalar',\n        icon: _barrel_optimize_names_BuildingOfficeIcon_ChevronDownIcon_Cog6ToothIcon_DocumentIcon_DocumentTextIcon_EnvelopeIcon_HomeIcon_PlusIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        isDropdown: true,\n        children: [] // Bu dinamik olarak doldurulacak\n    },\n    {\n        name: 'Şirket Bilgileri',\n        href: '/admin/company-settings',\n        icon: _barrel_optimize_names_BuildingOfficeIcon_ChevronDownIcon_Cog6ToothIcon_DocumentIcon_DocumentTextIcon_EnvelopeIcon_HomeIcon_PlusIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n    },\n    {\n        name: 'Genel Ayarlar',\n        href: '/admin/settings',\n        icon: _barrel_optimize_names_BuildingOfficeIcon_ChevronDownIcon_Cog6ToothIcon_DocumentIcon_DocumentTextIcon_EnvelopeIcon_HomeIcon_PlusIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n    }\n];\nfunction Sidebar(param) {\n    let { open, setOpen } = param;\n    var _user_name, _user_name1;\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const { user, logout } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const { settings } = (0,_contexts_SettingsContext__WEBPACK_IMPORTED_MODULE_5__.useSettings)();\n    const [pages, setPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [openDropdowns, setOpenDropdowns] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        'Sayfalar'\n    ]);\n    // Sayfaları yükle\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Sidebar.useEffect\": ()=>{\n            const fetchPages = {\n                \"Sidebar.useEffect.fetchPages\": async ()=>{\n                    try {\n                        const response = await (0,_utils_api__WEBPACK_IMPORTED_MODULE_6__.fetchFromAdminApi)('/pages.php');\n                        if (response.success && response.data) {\n                            setPages(response.data);\n                        }\n                    } catch (error) {\n                        console.error('Sayfalar yüklenirken hata:', error);\n                    }\n                }\n            }[\"Sidebar.useEffect.fetchPages\"];\n            fetchPages();\n        }\n    }[\"Sidebar.useEffect\"], []);\n    // Sayfalar dropdown'ını dinamik olarak güncelle\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Sidebar.useEffect\": ()=>{\n            const pagesNavItem = navigation.find({\n                \"Sidebar.useEffect.pagesNavItem\": (item)=>item.name === 'Sayfalar'\n            }[\"Sidebar.useEffect.pagesNavItem\"]);\n            if (pagesNavItem && pagesNavItem.children) {\n                pagesNavItem.children = [\n                    {\n                        name: 'Ana Sayfa',\n                        href: '/admin/homepage',\n                        icon: _barrel_optimize_names_BuildingOfficeIcon_ChevronDownIcon_Cog6ToothIcon_DocumentIcon_DocumentTextIcon_EnvelopeIcon_HomeIcon_PlusIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n                    },\n                    ...pages.map({\n                        \"Sidebar.useEffect\": (page)=>({\n                                name: page.title_tr,\n                                href: \"/admin/pages/\".concat(page.slug),\n                                icon: _barrel_optimize_names_BuildingOfficeIcon_ChevronDownIcon_Cog6ToothIcon_DocumentIcon_DocumentTextIcon_EnvelopeIcon_HomeIcon_PlusIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n                            })\n                    }[\"Sidebar.useEffect\"]),\n                    {\n                        name: '+ Yeni Sayfa Ekle',\n                        href: '/admin/pages/new',\n                        icon: _barrel_optimize_names_BuildingOfficeIcon_ChevronDownIcon_Cog6ToothIcon_DocumentIcon_DocumentTextIcon_EnvelopeIcon_HomeIcon_PlusIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"]\n                    }\n                ];\n            }\n        }\n    }[\"Sidebar.useEffect\"], [\n        pages\n    ]);\n    const toggleDropdown = (name)=>{\n        setOpenDropdowns((prev)=>prev.includes(name) ? prev.filter((item)=>item !== name) : [\n                ...prev,\n                name\n            ]);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_16__.Transition.Root, {\n                show: open,\n                as: react__WEBPACK_IMPORTED_MODULE_1__.Fragment,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_17__.Dialog, {\n                    as: \"div\",\n                    className: \"relative z-50 lg:hidden\",\n                    onClose: setOpen,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_16__.Transition.Child, {\n                            as: react__WEBPACK_IMPORTED_MODULE_1__.Fragment,\n                            enter: \"transition-opacity ease-linear duration-300\",\n                            enterFrom: \"opacity-0\",\n                            enterTo: \"opacity-100\",\n                            leave: \"transition-opacity ease-linear duration-300\",\n                            leaveFrom: \"opacity-100\",\n                            leaveTo: \"opacity-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"fixed inset-0 bg-secondary-900/80\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\components\\\\admin\\\\layout\\\\Sidebar.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\components\\\\admin\\\\layout\\\\Sidebar.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"fixed inset-0 flex\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_16__.Transition.Child, {\n                                as: react__WEBPACK_IMPORTED_MODULE_1__.Fragment,\n                                enter: \"transition ease-in-out duration-300 transform\",\n                                enterFrom: \"-translate-x-full\",\n                                enterTo: \"translate-x-0\",\n                                leave: \"transition ease-in-out duration-300 transform\",\n                                leaveFrom: \"translate-x-0\",\n                                leaveTo: \"-translate-x-full\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_17__.Dialog.Panel, {\n                                    className: \"relative mr-16 flex w-full max-w-xs flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_Transition_headlessui_react__WEBPACK_IMPORTED_MODULE_16__.Transition.Child, {\n                                            as: react__WEBPACK_IMPORTED_MODULE_1__.Fragment,\n                                            enter: \"ease-in-out duration-300\",\n                                            enterFrom: \"opacity-0\",\n                                            enterTo: \"opacity-100\",\n                                            leave: \"ease-in-out duration-300\",\n                                            leaveFrom: \"opacity-100\",\n                                            leaveTo: \"opacity-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute left-full top-0 flex w-16 justify-center pt-5\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    className: \"-m-2.5 p-2.5\",\n                                                    onClick: ()=>setOpen(false),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"sr-only\",\n                                                            children: \"Close sidebar\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\components\\\\admin\\\\layout\\\\Sidebar.tsx\",\n                                                            lineNumber: 149,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BuildingOfficeIcon_ChevronDownIcon_Cog6ToothIcon_DocumentIcon_DocumentTextIcon_EnvelopeIcon_HomeIcon_PlusIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"h-6 w-6 text-white\",\n                                                            \"aria-hidden\": \"true\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\components\\\\admin\\\\layout\\\\Sidebar.tsx\",\n                                                            lineNumber: 150,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\components\\\\admin\\\\layout\\\\Sidebar.tsx\",\n                                                    lineNumber: 144,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\components\\\\admin\\\\layout\\\\Sidebar.tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\components\\\\admin\\\\layout\\\\Sidebar.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex grow flex-col gap-y-5 overflow-y-auto bg-white px-6 pb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex h-16 shrink-0 items-center\",\n                                                    children: [\n                                                        (settings === null || settings === void 0 ? void 0 : settings.site_logo) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: \"h-8 w-auto\",\n                                                            src: settings.site_logo,\n                                                            alt: \"RENERO\",\n                                                            width: 32,\n                                                            height: 32\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\components\\\\admin\\\\layout\\\\Sidebar.tsx\",\n                                                            lineNumber: 157,\n                                                            columnNumber: 23\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-8 w-8 bg-primary-600 rounded flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white font-bold text-sm\",\n                                                                children: \"R\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\components\\\\admin\\\\layout\\\\Sidebar.tsx\",\n                                                                lineNumber: 166,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\components\\\\admin\\\\layout\\\\Sidebar.tsx\",\n                                                            lineNumber: 165,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"ml-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                                    className: \"text-lg font-bold text-secondary-900\",\n                                                                    children: (settings === null || settings === void 0 ? void 0 : settings.company_name) || 'RENERO'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\components\\\\admin\\\\layout\\\\Sidebar.tsx\",\n                                                                    lineNumber: 170,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-secondary-600 -mt-1\",\n                                                                    children: \"Admin Panel\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\components\\\\admin\\\\layout\\\\Sidebar.tsx\",\n                                                                    lineNumber: 173,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\components\\\\admin\\\\layout\\\\Sidebar.tsx\",\n                                                            lineNumber: 169,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\components\\\\admin\\\\layout\\\\Sidebar.tsx\",\n                                                    lineNumber: 155,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                                    className: \"flex flex-1 flex-col\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        role: \"list\",\n                                                        className: \"flex flex-1 flex-col gap-y-7\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                    role: \"list\",\n                                                                    className: \"-mx-2 space-y-1\",\n                                                                    children: navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: item.isDropdown ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: ()=>toggleDropdown(item.name),\n                                                                                        className: \"group flex w-full gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold text-secondary-700 hover:bg-secondary-50 hover:text-primary-600\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                                                                className: \"h-6 w-6 shrink-0 text-secondary-400 group-hover:text-primary-600\",\n                                                                                                \"aria-hidden\": \"true\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\components\\\\admin\\\\layout\\\\Sidebar.tsx\",\n                                                                                                lineNumber: 188,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            item.name,\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BuildingOfficeIcon_ChevronDownIcon_Cog6ToothIcon_DocumentIcon_DocumentTextIcon_EnvelopeIcon_HomeIcon_PlusIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                                className: (0,clsx__WEBPACK_IMPORTED_MODULE_8__[\"default\"])('ml-auto h-5 w-5 transition-transform', openDropdowns.includes(item.name) ? 'rotate-180' : '')\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\components\\\\admin\\\\layout\\\\Sidebar.tsx\",\n                                                                                                lineNumber: 193,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\components\\\\admin\\\\layout\\\\Sidebar.tsx\",\n                                                                                        lineNumber: 184,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    openDropdowns.includes(item.name) && item.children && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                                        className: \"mt-1 ml-6 space-y-1\",\n                                                                                        children: item.children.map((child)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                                                    href: child.href || '#',\n                                                                                                    className: (0,clsx__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(pathname === child.href ? 'sidebar-nav-item-active' : 'sidebar-nav-item-inactive', 'group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold'),\n                                                                                                    onClick: ()=>setOpen(false),\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(child.icon, {\n                                                                                                            className: (0,clsx__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(pathname === child.href ? 'text-primary-600' : 'text-secondary-400 group-hover:text-primary-600', 'h-5 w-5 shrink-0'),\n                                                                                                            \"aria-hidden\": \"true\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\components\\\\admin\\\\layout\\\\Sidebar.tsx\",\n                                                                                                            lineNumber: 214,\n                                                                                                            columnNumber: 45\n                                                                                                        }, this),\n                                                                                                        child.name\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\components\\\\admin\\\\layout\\\\Sidebar.tsx\",\n                                                                                                    lineNumber: 204,\n                                                                                                    columnNumber: 43\n                                                                                                }, this)\n                                                                                            }, child.name, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\components\\\\admin\\\\layout\\\\Sidebar.tsx\",\n                                                                                                lineNumber: 203,\n                                                                                                columnNumber: 41\n                                                                                            }, this))\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\components\\\\admin\\\\layout\\\\Sidebar.tsx\",\n                                                                                        lineNumber: 201,\n                                                                                        columnNumber: 37\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\components\\\\admin\\\\layout\\\\Sidebar.tsx\",\n                                                                                lineNumber: 183,\n                                                                                columnNumber: 33\n                                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                                href: item.href || '#',\n                                                                                className: (0,clsx__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(pathname === item.href ? 'sidebar-nav-item-active' : 'sidebar-nav-item-inactive', 'group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold'),\n                                                                                onClick: ()=>setOpen(false),\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                                                        className: (0,clsx__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(pathname === item.href ? 'text-primary-600' : 'text-secondary-400 group-hover:text-primary-600', 'h-6 w-6 shrink-0'),\n                                                                                        \"aria-hidden\": \"true\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\components\\\\admin\\\\layout\\\\Sidebar.tsx\",\n                                                                                        lineNumber: 241,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    item.name\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\components\\\\admin\\\\layout\\\\Sidebar.tsx\",\n                                                                                lineNumber: 231,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        }, item.name, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\components\\\\admin\\\\layout\\\\Sidebar.tsx\",\n                                                                            lineNumber: 181,\n                                                                            columnNumber: 29\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\components\\\\admin\\\\layout\\\\Sidebar.tsx\",\n                                                                    lineNumber: 179,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\components\\\\admin\\\\layout\\\\Sidebar.tsx\",\n                                                                lineNumber: 178,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                className: \"mt-auto\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-x-4 px-2 py-3 text-sm font-semibold leading-6 text-secondary-900\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"h-8 w-8 rounded-full bg-primary-600 flex items-center justify-center\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm font-medium text-white\",\n                                                                                    children: user === null || user === void 0 ? void 0 : (_user_name = user.name) === null || _user_name === void 0 ? void 0 : _user_name.charAt(0).toUpperCase()\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\components\\\\admin\\\\layout\\\\Sidebar.tsx\",\n                                                                                    lineNumber: 260,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\components\\\\admin\\\\layout\\\\Sidebar.tsx\",\n                                                                                lineNumber: 259,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"sr-only\",\n                                                                                children: \"Your profile\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\components\\\\admin\\\\layout\\\\Sidebar.tsx\",\n                                                                                lineNumber: 264,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-sm font-medium text-secondary-900\",\n                                                                                        children: user === null || user === void 0 ? void 0 : user.name\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\components\\\\admin\\\\layout\\\\Sidebar.tsx\",\n                                                                                        lineNumber: 266,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-xs text-secondary-500\",\n                                                                                        children: user === null || user === void 0 ? void 0 : user.email\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\components\\\\admin\\\\layout\\\\Sidebar.tsx\",\n                                                                                        lineNumber: 267,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\components\\\\admin\\\\layout\\\\Sidebar.tsx\",\n                                                                                lineNumber: 265,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\components\\\\admin\\\\layout\\\\Sidebar.tsx\",\n                                                                        lineNumber: 258,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: logout,\n                                                                        className: \"group -mx-2 flex w-full gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold text-secondary-700 hover:bg-secondary-50 hover:text-primary-600\",\n                                                                        children: \"\\xc7ıkış Yap\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\components\\\\admin\\\\layout\\\\Sidebar.tsx\",\n                                                                        lineNumber: 270,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\components\\\\admin\\\\layout\\\\Sidebar.tsx\",\n                                                                lineNumber: 257,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\components\\\\admin\\\\layout\\\\Sidebar.tsx\",\n                                                        lineNumber: 177,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\components\\\\admin\\\\layout\\\\Sidebar.tsx\",\n                                                    lineNumber: 176,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\components\\\\admin\\\\layout\\\\Sidebar.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\components\\\\admin\\\\layout\\\\Sidebar.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\components\\\\admin\\\\layout\\\\Sidebar.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\components\\\\admin\\\\layout\\\\Sidebar.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\components\\\\admin\\\\layout\\\\Sidebar.tsx\",\n                    lineNumber: 110,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\components\\\\admin\\\\layout\\\\Sidebar.tsx\",\n                lineNumber: 109,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-64 lg:flex-col\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex grow flex-col gap-y-5 overflow-y-auto border-r border-secondary-200 bg-white px-5 pb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex h-16 shrink-0 items-center\",\n                            children: [\n                                (settings === null || settings === void 0 ? void 0 : settings.site_logo) && settings.site_logo.trim() !== '' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"h-8 w-auto\",\n                                    src: settings.site_logo,\n                                    alt: \"RENERO\",\n                                    width: 32,\n                                    height: 32\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\components\\\\admin\\\\layout\\\\Sidebar.tsx\",\n                                    lineNumber: 291,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-8 w-8 bg-primary-600 rounded flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white font-bold text-sm\",\n                                        children: \"R\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\components\\\\admin\\\\layout\\\\Sidebar.tsx\",\n                                        lineNumber: 300,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\components\\\\admin\\\\layout\\\\Sidebar.tsx\",\n                                    lineNumber: 299,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"ml-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-lg font-bold text-secondary-900\",\n                                            children: \"RENERO\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\components\\\\admin\\\\layout\\\\Sidebar.tsx\",\n                                            lineNumber: 304,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-secondary-600 -mt-1\",\n                                            children: \"Admin Panel\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\components\\\\admin\\\\layout\\\\Sidebar.tsx\",\n                                            lineNumber: 307,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\components\\\\admin\\\\layout\\\\Sidebar.tsx\",\n                                    lineNumber: 303,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\components\\\\admin\\\\layout\\\\Sidebar.tsx\",\n                            lineNumber: 289,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"pt-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"flex flex-1 flex-col\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    role: \"list\",\n                                    className: \"flex flex-1 flex-col gap-y-7\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                role: \"list\",\n                                                className: \"-mx-2 space-y-1\",\n                                                children: navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                            href: item.href,\n                                                            className: (0,clsx__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(pathname === item.href ? 'sidebar-nav-item-active' : 'sidebar-nav-item-inactive', 'group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold'),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                                    className: (0,clsx__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(pathname === item.href ? 'text-primary-600' : 'text-secondary-400 group-hover:text-primary-600', 'h-6 w-6 shrink-0'),\n                                                                    \"aria-hidden\": \"true\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\components\\\\admin\\\\layout\\\\Sidebar.tsx\",\n                                                                    lineNumber: 328,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                item.name\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\components\\\\admin\\\\layout\\\\Sidebar.tsx\",\n                                                            lineNumber: 319,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, item.name, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\components\\\\admin\\\\layout\\\\Sidebar.tsx\",\n                                                        lineNumber: 318,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\components\\\\admin\\\\layout\\\\Sidebar.tsx\",\n                                                lineNumber: 316,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\components\\\\admin\\\\layout\\\\Sidebar.tsx\",\n                                            lineNumber: 315,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"mt-auto\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-x-4 px-2 py-3 text-sm font-semibold leading-6 text-secondary-900\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-8 w-8 rounded-full bg-primary-600 flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-white\",\n                                                                children: user === null || user === void 0 ? void 0 : (_user_name1 = user.name) === null || _user_name1 === void 0 ? void 0 : _user_name1.charAt(0).toUpperCase()\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\components\\\\admin\\\\layout\\\\Sidebar.tsx\",\n                                                                lineNumber: 346,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\components\\\\admin\\\\layout\\\\Sidebar.tsx\",\n                                                            lineNumber: 345,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"sr-only\",\n                                                            children: \"Your profile\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\components\\\\admin\\\\layout\\\\Sidebar.tsx\",\n                                                            lineNumber: 350,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-medium text-secondary-900\",\n                                                                    children: user === null || user === void 0 ? void 0 : user.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\components\\\\admin\\\\layout\\\\Sidebar.tsx\",\n                                                                    lineNumber: 352,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-secondary-500\",\n                                                                    children: user === null || user === void 0 ? void 0 : user.email\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\components\\\\admin\\\\layout\\\\Sidebar.tsx\",\n                                                                    lineNumber: 353,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\components\\\\admin\\\\layout\\\\Sidebar.tsx\",\n                                                            lineNumber: 351,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\components\\\\admin\\\\layout\\\\Sidebar.tsx\",\n                                                    lineNumber: 344,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: logout,\n                                                    className: \"group -mx-2 flex w-full gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold text-secondary-700 hover:bg-secondary-50 hover:text-primary-600\",\n                                                    children: \"\\xc7ıkış Yap\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\components\\\\admin\\\\layout\\\\Sidebar.tsx\",\n                                                    lineNumber: 356,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\components\\\\admin\\\\layout\\\\Sidebar.tsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\components\\\\admin\\\\layout\\\\Sidebar.tsx\",\n                                    lineNumber: 314,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\components\\\\admin\\\\layout\\\\Sidebar.tsx\",\n                                lineNumber: 313,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\components\\\\admin\\\\layout\\\\Sidebar.tsx\",\n                            lineNumber: 312,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\components\\\\admin\\\\layout\\\\Sidebar.tsx\",\n                    lineNumber: 288,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RENERO\\\\next_renero\\\\src\\\\components\\\\admin\\\\layout\\\\Sidebar.tsx\",\n                lineNumber: 287,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Sidebar, \"IoHt5qCAdzZRxmM9u6NsrVsYxKc=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth,\n        _contexts_SettingsContext__WEBPACK_IMPORTED_MODULE_5__.useSettings\n    ];\n});\n_c = Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/layout/Sidebar.tsx\n"));

/***/ })

});