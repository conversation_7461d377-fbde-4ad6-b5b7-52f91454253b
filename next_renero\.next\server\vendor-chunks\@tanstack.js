"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tanstack";
exports.ids = ["vendor-chunks/@tanstack"];
exports.modules = {

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/lib/focusManager.mjs":
/*!**********************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/lib/focusManager.mjs ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FocusManager: () => (/* binding */ FocusManager),\n/* harmony export */   focusManager: () => (/* binding */ focusManager)\n/* harmony export */ });\n/* harmony import */ var _subscribable_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./subscribable.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/subscribable.mjs\");\n/* harmony import */ var _utils_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/utils.mjs\");\n\n\n\nclass FocusManager extends _subscribable_mjs__WEBPACK_IMPORTED_MODULE_0__.Subscribable {\n  constructor() {\n    super();\n\n    this.setup = onFocus => {\n      // addEventListener does not exist in React Native, but window does\n      // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n      if (!_utils_mjs__WEBPACK_IMPORTED_MODULE_1__.isServer && window.addEventListener) {\n        const listener = () => onFocus(); // Listen to visibillitychange and focus\n\n\n        window.addEventListener('visibilitychange', listener, false);\n        window.addEventListener('focus', listener, false);\n        return () => {\n          // Be sure to unsubscribe if a new handler is set\n          window.removeEventListener('visibilitychange', listener);\n          window.removeEventListener('focus', listener);\n        };\n      }\n\n      return;\n    };\n  }\n\n  onSubscribe() {\n    if (!this.cleanup) {\n      this.setEventListener(this.setup);\n    }\n  }\n\n  onUnsubscribe() {\n    if (!this.hasListeners()) {\n      var _this$cleanup;\n\n      (_this$cleanup = this.cleanup) == null ? void 0 : _this$cleanup.call(this);\n      this.cleanup = undefined;\n    }\n  }\n\n  setEventListener(setup) {\n    var _this$cleanup2;\n\n    this.setup = setup;\n    (_this$cleanup2 = this.cleanup) == null ? void 0 : _this$cleanup2.call(this);\n    this.cleanup = setup(focused => {\n      if (typeof focused === 'boolean') {\n        this.setFocused(focused);\n      } else {\n        this.onFocus();\n      }\n    });\n  }\n\n  setFocused(focused) {\n    const changed = this.focused !== focused;\n\n    if (changed) {\n      this.focused = focused;\n      this.onFocus();\n    }\n  }\n\n  onFocus() {\n    this.listeners.forEach(({\n      listener\n    }) => {\n      listener();\n    });\n  }\n\n  isFocused() {\n    if (typeof this.focused === 'boolean') {\n      return this.focused;\n    } // document global can be unavailable in react native\n\n\n    if (typeof document === 'undefined') {\n      return true;\n    }\n\n    return [undefined, 'visible', 'prerender'].includes(document.visibilityState);\n  }\n\n}\nconst focusManager = new FocusManager();\n\n\n//# sourceMappingURL=focusManager.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/lib/focusManager.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/lib/infiniteQueryBehavior.mjs":
/*!*******************************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/lib/infiniteQueryBehavior.mjs ***!
  \*******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getNextPageParam: () => (/* binding */ getNextPageParam),\n/* harmony export */   getPreviousPageParam: () => (/* binding */ getPreviousPageParam),\n/* harmony export */   hasNextPage: () => (/* binding */ hasNextPage),\n/* harmony export */   hasPreviousPage: () => (/* binding */ hasPreviousPage),\n/* harmony export */   infiniteQueryBehavior: () => (/* binding */ infiniteQueryBehavior)\n/* harmony export */ });\nfunction infiniteQueryBehavior() {\n  return {\n    onFetch: context => {\n      context.fetchFn = () => {\n        var _context$fetchOptions, _context$fetchOptions2, _context$fetchOptions3, _context$fetchOptions4, _context$state$data, _context$state$data2;\n\n        const refetchPage = (_context$fetchOptions = context.fetchOptions) == null ? void 0 : (_context$fetchOptions2 = _context$fetchOptions.meta) == null ? void 0 : _context$fetchOptions2.refetchPage;\n        const fetchMore = (_context$fetchOptions3 = context.fetchOptions) == null ? void 0 : (_context$fetchOptions4 = _context$fetchOptions3.meta) == null ? void 0 : _context$fetchOptions4.fetchMore;\n        const pageParam = fetchMore == null ? void 0 : fetchMore.pageParam;\n        const isFetchingNextPage = (fetchMore == null ? void 0 : fetchMore.direction) === 'forward';\n        const isFetchingPreviousPage = (fetchMore == null ? void 0 : fetchMore.direction) === 'backward';\n        const oldPages = ((_context$state$data = context.state.data) == null ? void 0 : _context$state$data.pages) || [];\n        const oldPageParams = ((_context$state$data2 = context.state.data) == null ? void 0 : _context$state$data2.pageParams) || [];\n        let newPageParams = oldPageParams;\n        let cancelled = false;\n\n        const addSignalProperty = object => {\n          Object.defineProperty(object, 'signal', {\n            enumerable: true,\n            get: () => {\n              var _context$signal;\n\n              if ((_context$signal = context.signal) != null && _context$signal.aborted) {\n                cancelled = true;\n              } else {\n                var _context$signal2;\n\n                (_context$signal2 = context.signal) == null ? void 0 : _context$signal2.addEventListener('abort', () => {\n                  cancelled = true;\n                });\n              }\n\n              return context.signal;\n            }\n          });\n        }; // Get query function\n\n\n        const queryFn = context.options.queryFn || (() => Promise.reject(\"Missing queryFn for queryKey '\" + context.options.queryHash + \"'\"));\n\n        const buildNewPages = (pages, param, page, previous) => {\n          newPageParams = previous ? [param, ...newPageParams] : [...newPageParams, param];\n          return previous ? [page, ...pages] : [...pages, page];\n        }; // Create function to fetch a page\n\n\n        const fetchPage = (pages, manual, param, previous) => {\n          if (cancelled) {\n            return Promise.reject('Cancelled');\n          }\n\n          if (typeof param === 'undefined' && !manual && pages.length) {\n            return Promise.resolve(pages);\n          }\n\n          const queryFnContext = {\n            queryKey: context.queryKey,\n            pageParam: param,\n            meta: context.options.meta\n          };\n          addSignalProperty(queryFnContext);\n          const queryFnResult = queryFn(queryFnContext);\n          const promise = Promise.resolve(queryFnResult).then(page => buildNewPages(pages, param, page, previous));\n          return promise;\n        };\n\n        let promise; // Fetch first page?\n\n        if (!oldPages.length) {\n          promise = fetchPage([]);\n        } // Fetch next page?\n        else if (isFetchingNextPage) {\n          const manual = typeof pageParam !== 'undefined';\n          const param = manual ? pageParam : getNextPageParam(context.options, oldPages);\n          promise = fetchPage(oldPages, manual, param);\n        } // Fetch previous page?\n        else if (isFetchingPreviousPage) {\n          const manual = typeof pageParam !== 'undefined';\n          const param = manual ? pageParam : getPreviousPageParam(context.options, oldPages);\n          promise = fetchPage(oldPages, manual, param, true);\n        } // Refetch pages\n        else {\n          newPageParams = [];\n          const manual = typeof context.options.getNextPageParam === 'undefined';\n          const shouldFetchFirstPage = refetchPage && oldPages[0] ? refetchPage(oldPages[0], 0, oldPages) : true; // Fetch first page\n\n          promise = shouldFetchFirstPage ? fetchPage([], manual, oldPageParams[0]) : Promise.resolve(buildNewPages([], oldPageParams[0], oldPages[0])); // Fetch remaining pages\n\n          for (let i = 1; i < oldPages.length; i++) {\n            promise = promise.then(pages => {\n              const shouldFetchNextPage = refetchPage && oldPages[i] ? refetchPage(oldPages[i], i, oldPages) : true;\n\n              if (shouldFetchNextPage) {\n                const param = manual ? oldPageParams[i] : getNextPageParam(context.options, pages);\n                return fetchPage(pages, manual, param);\n              }\n\n              return Promise.resolve(buildNewPages(pages, oldPageParams[i], oldPages[i]));\n            });\n          }\n        }\n\n        const finalPromise = promise.then(pages => ({\n          pages,\n          pageParams: newPageParams\n        }));\n        return finalPromise;\n      };\n    }\n  };\n}\nfunction getNextPageParam(options, pages) {\n  return options.getNextPageParam == null ? void 0 : options.getNextPageParam(pages[pages.length - 1], pages);\n}\nfunction getPreviousPageParam(options, pages) {\n  return options.getPreviousPageParam == null ? void 0 : options.getPreviousPageParam(pages[0], pages);\n}\n/**\n * Checks if there is a next page.\n * Returns `undefined` if it cannot be determined.\n */\n\nfunction hasNextPage(options, pages) {\n  if (options.getNextPageParam && Array.isArray(pages)) {\n    const nextPageParam = getNextPageParam(options, pages);\n    return typeof nextPageParam !== 'undefined' && nextPageParam !== null && nextPageParam !== false;\n  }\n\n  return;\n}\n/**\n * Checks if there is a previous page.\n * Returns `undefined` if it cannot be determined.\n */\n\nfunction hasPreviousPage(options, pages) {\n  if (options.getPreviousPageParam && Array.isArray(pages)) {\n    const previousPageParam = getPreviousPageParam(options, pages);\n    return typeof previousPageParam !== 'undefined' && previousPageParam !== null && previousPageParam !== false;\n  }\n\n  return;\n}\n\n\n//# sourceMappingURL=infiniteQueryBehavior.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/lib/infiniteQueryBehavior.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/lib/logger.mjs":
/*!****************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/lib/logger.mjs ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultLogger: () => (/* binding */ defaultLogger)\n/* harmony export */ });\nconst defaultLogger = console;\n\n\n//# sourceMappingURL=logger.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRhbnN0YWNrL3F1ZXJ5LWNvcmUvYnVpbGQvbGliL2xvZ2dlci5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBOztBQUV5QjtBQUN6QiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxiZXJrYVxcRGVza3RvcFxcUkVORVJPXFxuZXh0X3JlbmVyb1xcbm9kZV9tb2R1bGVzXFxAdGFuc3RhY2tcXHF1ZXJ5LWNvcmVcXGJ1aWxkXFxsaWJcXGxvZ2dlci5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgZGVmYXVsdExvZ2dlciA9IGNvbnNvbGU7XG5cbmV4cG9ydCB7IGRlZmF1bHRMb2dnZXIgfTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWxvZ2dlci5tanMubWFwXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/lib/logger.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/lib/mutation.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/lib/mutation.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Mutation: () => (/* binding */ Mutation),\n/* harmony export */   getDefaultState: () => (/* binding */ getDefaultState)\n/* harmony export */ });\n/* harmony import */ var _logger_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./logger.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/logger.mjs\");\n/* harmony import */ var _notifyManager_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./notifyManager.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/notifyManager.mjs\");\n/* harmony import */ var _removable_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./removable.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/removable.mjs\");\n/* harmony import */ var _retryer_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./retryer.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/retryer.mjs\");\n\n\n\n\n\n// CLASS\nclass Mutation extends _removable_mjs__WEBPACK_IMPORTED_MODULE_0__.Removable {\n  constructor(config) {\n    super();\n    this.defaultOptions = config.defaultOptions;\n    this.mutationId = config.mutationId;\n    this.mutationCache = config.mutationCache;\n    this.logger = config.logger || _logger_mjs__WEBPACK_IMPORTED_MODULE_1__.defaultLogger;\n    this.observers = [];\n    this.state = config.state || getDefaultState();\n    this.setOptions(config.options);\n    this.scheduleGc();\n  }\n\n  setOptions(options) {\n    this.options = { ...this.defaultOptions,\n      ...options\n    };\n    this.updateCacheTime(this.options.cacheTime);\n  }\n\n  get meta() {\n    return this.options.meta;\n  }\n\n  setState(state) {\n    this.dispatch({\n      type: 'setState',\n      state\n    });\n  }\n\n  addObserver(observer) {\n    if (!this.observers.includes(observer)) {\n      this.observers.push(observer); // Stop the mutation from being garbage collected\n\n      this.clearGcTimeout();\n      this.mutationCache.notify({\n        type: 'observerAdded',\n        mutation: this,\n        observer\n      });\n    }\n  }\n\n  removeObserver(observer) {\n    this.observers = this.observers.filter(x => x !== observer);\n    this.scheduleGc();\n    this.mutationCache.notify({\n      type: 'observerRemoved',\n      mutation: this,\n      observer\n    });\n  }\n\n  optionalRemove() {\n    if (!this.observers.length) {\n      if (this.state.status === 'loading') {\n        this.scheduleGc();\n      } else {\n        this.mutationCache.remove(this);\n      }\n    }\n  }\n\n  continue() {\n    var _this$retryer$continu, _this$retryer;\n\n    return (_this$retryer$continu = (_this$retryer = this.retryer) == null ? void 0 : _this$retryer.continue()) != null ? _this$retryer$continu : this.execute();\n  }\n\n  async execute() {\n    const executeMutation = () => {\n      var _this$options$retry;\n\n      this.retryer = (0,_retryer_mjs__WEBPACK_IMPORTED_MODULE_2__.createRetryer)({\n        fn: () => {\n          if (!this.options.mutationFn) {\n            return Promise.reject('No mutationFn found');\n          }\n\n          return this.options.mutationFn(this.state.variables);\n        },\n        onFail: (failureCount, error) => {\n          this.dispatch({\n            type: 'failed',\n            failureCount,\n            error\n          });\n        },\n        onPause: () => {\n          this.dispatch({\n            type: 'pause'\n          });\n        },\n        onContinue: () => {\n          this.dispatch({\n            type: 'continue'\n          });\n        },\n        retry: (_this$options$retry = this.options.retry) != null ? _this$options$retry : 0,\n        retryDelay: this.options.retryDelay,\n        networkMode: this.options.networkMode\n      });\n      return this.retryer.promise;\n    };\n\n    const restored = this.state.status === 'loading';\n\n    try {\n      var _this$mutationCache$c3, _this$mutationCache$c4, _this$options$onSucce, _this$options2, _this$mutationCache$c5, _this$mutationCache$c6, _this$options$onSettl, _this$options3;\n\n      if (!restored) {\n        var _this$mutationCache$c, _this$mutationCache$c2, _this$options$onMutat, _this$options;\n\n        this.dispatch({\n          type: 'loading',\n          variables: this.options.variables\n        }); // Notify cache callback\n\n        await ((_this$mutationCache$c = (_this$mutationCache$c2 = this.mutationCache.config).onMutate) == null ? void 0 : _this$mutationCache$c.call(_this$mutationCache$c2, this.state.variables, this));\n        const context = await ((_this$options$onMutat = (_this$options = this.options).onMutate) == null ? void 0 : _this$options$onMutat.call(_this$options, this.state.variables));\n\n        if (context !== this.state.context) {\n          this.dispatch({\n            type: 'loading',\n            context,\n            variables: this.state.variables\n          });\n        }\n      }\n\n      const data = await executeMutation(); // Notify cache callback\n\n      await ((_this$mutationCache$c3 = (_this$mutationCache$c4 = this.mutationCache.config).onSuccess) == null ? void 0 : _this$mutationCache$c3.call(_this$mutationCache$c4, data, this.state.variables, this.state.context, this));\n      await ((_this$options$onSucce = (_this$options2 = this.options).onSuccess) == null ? void 0 : _this$options$onSucce.call(_this$options2, data, this.state.variables, this.state.context)); // Notify cache callback\n\n      await ((_this$mutationCache$c5 = (_this$mutationCache$c6 = this.mutationCache.config).onSettled) == null ? void 0 : _this$mutationCache$c5.call(_this$mutationCache$c6, data, null, this.state.variables, this.state.context, this));\n      await ((_this$options$onSettl = (_this$options3 = this.options).onSettled) == null ? void 0 : _this$options$onSettl.call(_this$options3, data, null, this.state.variables, this.state.context));\n      this.dispatch({\n        type: 'success',\n        data\n      });\n      return data;\n    } catch (error) {\n      try {\n        var _this$mutationCache$c7, _this$mutationCache$c8, _this$options$onError, _this$options4, _this$mutationCache$c9, _this$mutationCache$c10, _this$options$onSettl2, _this$options5;\n\n        // Notify cache callback\n        await ((_this$mutationCache$c7 = (_this$mutationCache$c8 = this.mutationCache.config).onError) == null ? void 0 : _this$mutationCache$c7.call(_this$mutationCache$c8, error, this.state.variables, this.state.context, this));\n\n        if (true) {\n          this.logger.error(error);\n        }\n\n        await ((_this$options$onError = (_this$options4 = this.options).onError) == null ? void 0 : _this$options$onError.call(_this$options4, error, this.state.variables, this.state.context)); // Notify cache callback\n\n        await ((_this$mutationCache$c9 = (_this$mutationCache$c10 = this.mutationCache.config).onSettled) == null ? void 0 : _this$mutationCache$c9.call(_this$mutationCache$c10, undefined, error, this.state.variables, this.state.context, this));\n        await ((_this$options$onSettl2 = (_this$options5 = this.options).onSettled) == null ? void 0 : _this$options$onSettl2.call(_this$options5, undefined, error, this.state.variables, this.state.context));\n        throw error;\n      } finally {\n        this.dispatch({\n          type: 'error',\n          error: error\n        });\n      }\n    }\n  }\n\n  dispatch(action) {\n    const reducer = state => {\n      switch (action.type) {\n        case 'failed':\n          return { ...state,\n            failureCount: action.failureCount,\n            failureReason: action.error\n          };\n\n        case 'pause':\n          return { ...state,\n            isPaused: true\n          };\n\n        case 'continue':\n          return { ...state,\n            isPaused: false\n          };\n\n        case 'loading':\n          return { ...state,\n            context: action.context,\n            data: undefined,\n            failureCount: 0,\n            failureReason: null,\n            error: null,\n            isPaused: !(0,_retryer_mjs__WEBPACK_IMPORTED_MODULE_2__.canFetch)(this.options.networkMode),\n            status: 'loading',\n            variables: action.variables\n          };\n\n        case 'success':\n          return { ...state,\n            data: action.data,\n            failureCount: 0,\n            failureReason: null,\n            error: null,\n            status: 'success',\n            isPaused: false\n          };\n\n        case 'error':\n          return { ...state,\n            data: undefined,\n            error: action.error,\n            failureCount: state.failureCount + 1,\n            failureReason: action.error,\n            isPaused: false,\n            status: 'error'\n          };\n\n        case 'setState':\n          return { ...state,\n            ...action.state\n          };\n      }\n    };\n\n    this.state = reducer(this.state);\n    _notifyManager_mjs__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(() => {\n      this.observers.forEach(observer => {\n        observer.onMutationUpdate(action);\n      });\n      this.mutationCache.notify({\n        mutation: this,\n        type: 'updated',\n        action\n      });\n    });\n  }\n\n}\nfunction getDefaultState() {\n  return {\n    context: undefined,\n    data: undefined,\n    error: null,\n    failureCount: 0,\n    failureReason: null,\n    isPaused: false,\n    status: 'idle',\n    variables: undefined\n  };\n}\n\n\n//# sourceMappingURL=mutation.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/lib/mutation.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/lib/mutationCache.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/lib/mutationCache.mjs ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MutationCache: () => (/* binding */ MutationCache)\n/* harmony export */ });\n/* harmony import */ var _notifyManager_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./notifyManager.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/notifyManager.mjs\");\n/* harmony import */ var _mutation_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./mutation.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/mutation.mjs\");\n/* harmony import */ var _utils_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/utils.mjs\");\n/* harmony import */ var _subscribable_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./subscribable.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/subscribable.mjs\");\n\n\n\n\n\n// CLASS\nclass MutationCache extends _subscribable_mjs__WEBPACK_IMPORTED_MODULE_0__.Subscribable {\n  constructor(config) {\n    super();\n    this.config = config || {};\n    this.mutations = [];\n    this.mutationId = 0;\n  }\n\n  build(client, options, state) {\n    const mutation = new _mutation_mjs__WEBPACK_IMPORTED_MODULE_1__.Mutation({\n      mutationCache: this,\n      logger: client.getLogger(),\n      mutationId: ++this.mutationId,\n      options: client.defaultMutationOptions(options),\n      state,\n      defaultOptions: options.mutationKey ? client.getMutationDefaults(options.mutationKey) : undefined\n    });\n    this.add(mutation);\n    return mutation;\n  }\n\n  add(mutation) {\n    this.mutations.push(mutation);\n    this.notify({\n      type: 'added',\n      mutation\n    });\n  }\n\n  remove(mutation) {\n    this.mutations = this.mutations.filter(x => x !== mutation);\n    this.notify({\n      type: 'removed',\n      mutation\n    });\n  }\n\n  clear() {\n    _notifyManager_mjs__WEBPACK_IMPORTED_MODULE_2__.notifyManager.batch(() => {\n      this.mutations.forEach(mutation => {\n        this.remove(mutation);\n      });\n    });\n  }\n\n  getAll() {\n    return this.mutations;\n  }\n\n  find(filters) {\n    if (typeof filters.exact === 'undefined') {\n      filters.exact = true;\n    }\n\n    return this.mutations.find(mutation => (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_3__.matchMutation)(filters, mutation));\n  }\n\n  findAll(filters) {\n    return this.mutations.filter(mutation => (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_3__.matchMutation)(filters, mutation));\n  }\n\n  notify(event) {\n    _notifyManager_mjs__WEBPACK_IMPORTED_MODULE_2__.notifyManager.batch(() => {\n      this.listeners.forEach(({\n        listener\n      }) => {\n        listener(event);\n      });\n    });\n  }\n\n  resumePausedMutations() {\n    var _this$resuming;\n\n    this.resuming = ((_this$resuming = this.resuming) != null ? _this$resuming : Promise.resolve()).then(() => {\n      const pausedMutations = this.mutations.filter(x => x.state.isPaused);\n      return _notifyManager_mjs__WEBPACK_IMPORTED_MODULE_2__.notifyManager.batch(() => pausedMutations.reduce((promise, mutation) => promise.then(() => mutation.continue().catch(_utils_mjs__WEBPACK_IMPORTED_MODULE_3__.noop)), Promise.resolve()));\n    }).then(() => {\n      this.resuming = undefined;\n    });\n    return this.resuming;\n  }\n\n}\n\n\n//# sourceMappingURL=mutationCache.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/lib/mutationCache.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/lib/notifyManager.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/lib/notifyManager.mjs ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createNotifyManager: () => (/* binding */ createNotifyManager),\n/* harmony export */   notifyManager: () => (/* binding */ notifyManager)\n/* harmony export */ });\n/* harmony import */ var _utils_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/utils.mjs\");\n\n\nfunction createNotifyManager() {\n  let queue = [];\n  let transactions = 0;\n\n  let notifyFn = callback => {\n    callback();\n  };\n\n  let batchNotifyFn = callback => {\n    callback();\n  };\n\n  const batch = callback => {\n    let result;\n    transactions++;\n\n    try {\n      result = callback();\n    } finally {\n      transactions--;\n\n      if (!transactions) {\n        flush();\n      }\n    }\n\n    return result;\n  };\n\n  const schedule = callback => {\n    if (transactions) {\n      queue.push(callback);\n    } else {\n      (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_0__.scheduleMicrotask)(() => {\n        notifyFn(callback);\n      });\n    }\n  };\n  /**\n   * All calls to the wrapped function will be batched.\n   */\n\n\n  const batchCalls = callback => {\n    return (...args) => {\n      schedule(() => {\n        callback(...args);\n      });\n    };\n  };\n\n  const flush = () => {\n    const originalQueue = queue;\n    queue = [];\n\n    if (originalQueue.length) {\n      (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_0__.scheduleMicrotask)(() => {\n        batchNotifyFn(() => {\n          originalQueue.forEach(callback => {\n            notifyFn(callback);\n          });\n        });\n      });\n    }\n  };\n  /**\n   * Use this method to set a custom notify function.\n   * This can be used to for example wrap notifications with `React.act` while running tests.\n   */\n\n\n  const setNotifyFunction = fn => {\n    notifyFn = fn;\n  };\n  /**\n   * Use this method to set a custom function to batch notifications together into a single tick.\n   * By default React Query will use the batch function provided by ReactDOM or React Native.\n   */\n\n\n  const setBatchNotifyFunction = fn => {\n    batchNotifyFn = fn;\n  };\n\n  return {\n    batch,\n    batchCalls,\n    schedule,\n    setNotifyFunction,\n    setBatchNotifyFunction\n  };\n} // SINGLETON\n\nconst notifyManager = createNotifyManager();\n\n\n//# sourceMappingURL=notifyManager.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/lib/notifyManager.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/lib/onlineManager.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/lib/onlineManager.mjs ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OnlineManager: () => (/* binding */ OnlineManager),\n/* harmony export */   onlineManager: () => (/* binding */ onlineManager)\n/* harmony export */ });\n/* harmony import */ var _subscribable_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./subscribable.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/subscribable.mjs\");\n/* harmony import */ var _utils_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/utils.mjs\");\n\n\n\nconst onlineEvents = ['online', 'offline'];\nclass OnlineManager extends _subscribable_mjs__WEBPACK_IMPORTED_MODULE_0__.Subscribable {\n  constructor() {\n    super();\n\n    this.setup = onOnline => {\n      // addEventListener does not exist in React Native, but window does\n      // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n      if (!_utils_mjs__WEBPACK_IMPORTED_MODULE_1__.isServer && window.addEventListener) {\n        const listener = () => onOnline(); // Listen to online\n\n\n        onlineEvents.forEach(event => {\n          window.addEventListener(event, listener, false);\n        });\n        return () => {\n          // Be sure to unsubscribe if a new handler is set\n          onlineEvents.forEach(event => {\n            window.removeEventListener(event, listener);\n          });\n        };\n      }\n\n      return;\n    };\n  }\n\n  onSubscribe() {\n    if (!this.cleanup) {\n      this.setEventListener(this.setup);\n    }\n  }\n\n  onUnsubscribe() {\n    if (!this.hasListeners()) {\n      var _this$cleanup;\n\n      (_this$cleanup = this.cleanup) == null ? void 0 : _this$cleanup.call(this);\n      this.cleanup = undefined;\n    }\n  }\n\n  setEventListener(setup) {\n    var _this$cleanup2;\n\n    this.setup = setup;\n    (_this$cleanup2 = this.cleanup) == null ? void 0 : _this$cleanup2.call(this);\n    this.cleanup = setup(online => {\n      if (typeof online === 'boolean') {\n        this.setOnline(online);\n      } else {\n        this.onOnline();\n      }\n    });\n  }\n\n  setOnline(online) {\n    const changed = this.online !== online;\n\n    if (changed) {\n      this.online = online;\n      this.onOnline();\n    }\n  }\n\n  onOnline() {\n    this.listeners.forEach(({\n      listener\n    }) => {\n      listener();\n    });\n  }\n\n  isOnline() {\n    if (typeof this.online === 'boolean') {\n      return this.online;\n    }\n\n    if (typeof navigator === 'undefined' || typeof navigator.onLine === 'undefined') {\n      return true;\n    }\n\n    return navigator.onLine;\n  }\n\n}\nconst onlineManager = new OnlineManager();\n\n\n//# sourceMappingURL=onlineManager.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/lib/onlineManager.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/lib/query.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/lib/query.mjs ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Query: () => (/* binding */ Query)\n/* harmony export */ });\n/* harmony import */ var _utils_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/utils.mjs\");\n/* harmony import */ var _logger_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./logger.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/logger.mjs\");\n/* harmony import */ var _notifyManager_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./notifyManager.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/notifyManager.mjs\");\n/* harmony import */ var _retryer_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./retryer.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/retryer.mjs\");\n/* harmony import */ var _removable_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./removable.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/removable.mjs\");\n\n\n\n\n\n\n// CLASS\nclass Query extends _removable_mjs__WEBPACK_IMPORTED_MODULE_0__.Removable {\n  constructor(config) {\n    super();\n    this.abortSignalConsumed = false;\n    this.defaultOptions = config.defaultOptions;\n    this.setOptions(config.options);\n    this.observers = [];\n    this.cache = config.cache;\n    this.logger = config.logger || _logger_mjs__WEBPACK_IMPORTED_MODULE_1__.defaultLogger;\n    this.queryKey = config.queryKey;\n    this.queryHash = config.queryHash;\n    this.initialState = config.state || getDefaultState(this.options);\n    this.state = this.initialState;\n    this.scheduleGc();\n  }\n\n  get meta() {\n    return this.options.meta;\n  }\n\n  setOptions(options) {\n    this.options = { ...this.defaultOptions,\n      ...options\n    };\n    this.updateCacheTime(this.options.cacheTime);\n  }\n\n  optionalRemove() {\n    if (!this.observers.length && this.state.fetchStatus === 'idle') {\n      this.cache.remove(this);\n    }\n  }\n\n  setData(newData, options) {\n    const data = (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_2__.replaceData)(this.state.data, newData, this.options); // Set data and mark it as cached\n\n    this.dispatch({\n      data,\n      type: 'success',\n      dataUpdatedAt: options == null ? void 0 : options.updatedAt,\n      manual: options == null ? void 0 : options.manual\n    });\n    return data;\n  }\n\n  setState(state, setStateOptions) {\n    this.dispatch({\n      type: 'setState',\n      state,\n      setStateOptions\n    });\n  }\n\n  cancel(options) {\n    var _this$retryer;\n\n    const promise = this.promise;\n    (_this$retryer = this.retryer) == null ? void 0 : _this$retryer.cancel(options);\n    return promise ? promise.then(_utils_mjs__WEBPACK_IMPORTED_MODULE_2__.noop).catch(_utils_mjs__WEBPACK_IMPORTED_MODULE_2__.noop) : Promise.resolve();\n  }\n\n  destroy() {\n    super.destroy();\n    this.cancel({\n      silent: true\n    });\n  }\n\n  reset() {\n    this.destroy();\n    this.setState(this.initialState);\n  }\n\n  isActive() {\n    return this.observers.some(observer => observer.options.enabled !== false);\n  }\n\n  isDisabled() {\n    return this.getObserversCount() > 0 && !this.isActive();\n  }\n\n  isStale() {\n    return this.state.isInvalidated || !this.state.dataUpdatedAt || this.observers.some(observer => observer.getCurrentResult().isStale);\n  }\n\n  isStaleByTime(staleTime = 0) {\n    return this.state.isInvalidated || !this.state.dataUpdatedAt || !(0,_utils_mjs__WEBPACK_IMPORTED_MODULE_2__.timeUntilStale)(this.state.dataUpdatedAt, staleTime);\n  }\n\n  onFocus() {\n    var _this$retryer2;\n\n    const observer = this.observers.find(x => x.shouldFetchOnWindowFocus());\n\n    if (observer) {\n      observer.refetch({\n        cancelRefetch: false\n      });\n    } // Continue fetch if currently paused\n\n\n    (_this$retryer2 = this.retryer) == null ? void 0 : _this$retryer2.continue();\n  }\n\n  onOnline() {\n    var _this$retryer3;\n\n    const observer = this.observers.find(x => x.shouldFetchOnReconnect());\n\n    if (observer) {\n      observer.refetch({\n        cancelRefetch: false\n      });\n    } // Continue fetch if currently paused\n\n\n    (_this$retryer3 = this.retryer) == null ? void 0 : _this$retryer3.continue();\n  }\n\n  addObserver(observer) {\n    if (!this.observers.includes(observer)) {\n      this.observers.push(observer); // Stop the query from being garbage collected\n\n      this.clearGcTimeout();\n      this.cache.notify({\n        type: 'observerAdded',\n        query: this,\n        observer\n      });\n    }\n  }\n\n  removeObserver(observer) {\n    if (this.observers.includes(observer)) {\n      this.observers = this.observers.filter(x => x !== observer);\n\n      if (!this.observers.length) {\n        // If the transport layer does not support cancellation\n        // we'll let the query continue so the result can be cached\n        if (this.retryer) {\n          if (this.abortSignalConsumed) {\n            this.retryer.cancel({\n              revert: true\n            });\n          } else {\n            this.retryer.cancelRetry();\n          }\n        }\n\n        this.scheduleGc();\n      }\n\n      this.cache.notify({\n        type: 'observerRemoved',\n        query: this,\n        observer\n      });\n    }\n  }\n\n  getObserversCount() {\n    return this.observers.length;\n  }\n\n  invalidate() {\n    if (!this.state.isInvalidated) {\n      this.dispatch({\n        type: 'invalidate'\n      });\n    }\n  }\n\n  fetch(options, fetchOptions) {\n    var _this$options$behavio, _context$fetchOptions;\n\n    if (this.state.fetchStatus !== 'idle') {\n      if (this.state.dataUpdatedAt && fetchOptions != null && fetchOptions.cancelRefetch) {\n        // Silently cancel current fetch if the user wants to cancel refetches\n        this.cancel({\n          silent: true\n        });\n      } else if (this.promise) {\n        var _this$retryer4;\n\n        // make sure that retries that were potentially cancelled due to unmounts can continue\n        (_this$retryer4 = this.retryer) == null ? void 0 : _this$retryer4.continueRetry(); // Return current promise if we are already fetching\n\n        return this.promise;\n      }\n    } // Update config if passed, otherwise the config from the last execution is used\n\n\n    if (options) {\n      this.setOptions(options);\n    } // Use the options from the first observer with a query function if no function is found.\n    // This can happen when the query is hydrated or created with setQueryData.\n\n\n    if (!this.options.queryFn) {\n      const observer = this.observers.find(x => x.options.queryFn);\n\n      if (observer) {\n        this.setOptions(observer.options);\n      }\n    }\n\n    if (true) {\n      if (!Array.isArray(this.options.queryKey)) {\n        this.logger.error(\"As of v4, queryKey needs to be an Array. If you are using a string like 'repoData', please change it to an Array, e.g. ['repoData']\");\n      }\n    }\n\n    const abortController = (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_2__.getAbortController)(); // Create query function context\n\n    const queryFnContext = {\n      queryKey: this.queryKey,\n      pageParam: undefined,\n      meta: this.meta\n    }; // Adds an enumerable signal property to the object that\n    // which sets abortSignalConsumed to true when the signal\n    // is read.\n\n    const addSignalProperty = object => {\n      Object.defineProperty(object, 'signal', {\n        enumerable: true,\n        get: () => {\n          if (abortController) {\n            this.abortSignalConsumed = true;\n            return abortController.signal;\n          }\n\n          return undefined;\n        }\n      });\n    };\n\n    addSignalProperty(queryFnContext); // Create fetch function\n\n    const fetchFn = () => {\n      if (!this.options.queryFn) {\n        return Promise.reject(\"Missing queryFn for queryKey '\" + this.options.queryHash + \"'\");\n      }\n\n      this.abortSignalConsumed = false;\n      return this.options.queryFn(queryFnContext);\n    }; // Trigger behavior hook\n\n\n    const context = {\n      fetchOptions,\n      options: this.options,\n      queryKey: this.queryKey,\n      state: this.state,\n      fetchFn\n    };\n    addSignalProperty(context);\n    (_this$options$behavio = this.options.behavior) == null ? void 0 : _this$options$behavio.onFetch(context); // Store state in case the current fetch needs to be reverted\n\n    this.revertState = this.state; // Set to fetching state if not already in it\n\n    if (this.state.fetchStatus === 'idle' || this.state.fetchMeta !== ((_context$fetchOptions = context.fetchOptions) == null ? void 0 : _context$fetchOptions.meta)) {\n      var _context$fetchOptions2;\n\n      this.dispatch({\n        type: 'fetch',\n        meta: (_context$fetchOptions2 = context.fetchOptions) == null ? void 0 : _context$fetchOptions2.meta\n      });\n    }\n\n    const onError = error => {\n      // Optimistically update state if needed\n      if (!((0,_retryer_mjs__WEBPACK_IMPORTED_MODULE_3__.isCancelledError)(error) && error.silent)) {\n        this.dispatch({\n          type: 'error',\n          error: error\n        });\n      }\n\n      if (!(0,_retryer_mjs__WEBPACK_IMPORTED_MODULE_3__.isCancelledError)(error)) {\n        var _this$cache$config$on, _this$cache$config, _this$cache$config$on2, _this$cache$config2;\n\n        // Notify cache callback\n        (_this$cache$config$on = (_this$cache$config = this.cache.config).onError) == null ? void 0 : _this$cache$config$on.call(_this$cache$config, error, this);\n        (_this$cache$config$on2 = (_this$cache$config2 = this.cache.config).onSettled) == null ? void 0 : _this$cache$config$on2.call(_this$cache$config2, this.state.data, error, this);\n\n        if (true) {\n          this.logger.error(error);\n        }\n      }\n\n      if (!this.isFetchingOptimistic) {\n        // Schedule query gc after fetching\n        this.scheduleGc();\n      }\n\n      this.isFetchingOptimistic = false;\n    }; // Try to fetch the data\n\n\n    this.retryer = (0,_retryer_mjs__WEBPACK_IMPORTED_MODULE_3__.createRetryer)({\n      fn: context.fetchFn,\n      abort: abortController == null ? void 0 : abortController.abort.bind(abortController),\n      onSuccess: data => {\n        var _this$cache$config$on3, _this$cache$config3, _this$cache$config$on4, _this$cache$config4;\n\n        if (typeof data === 'undefined') {\n          if (true) {\n            this.logger.error(\"Query data cannot be undefined. Please make sure to return a value other than undefined from your query function. Affected query key: \" + this.queryHash);\n          }\n\n          onError(new Error(this.queryHash + \" data is undefined\"));\n          return;\n        }\n\n        this.setData(data); // Notify cache callback\n\n        (_this$cache$config$on3 = (_this$cache$config3 = this.cache.config).onSuccess) == null ? void 0 : _this$cache$config$on3.call(_this$cache$config3, data, this);\n        (_this$cache$config$on4 = (_this$cache$config4 = this.cache.config).onSettled) == null ? void 0 : _this$cache$config$on4.call(_this$cache$config4, data, this.state.error, this);\n\n        if (!this.isFetchingOptimistic) {\n          // Schedule query gc after fetching\n          this.scheduleGc();\n        }\n\n        this.isFetchingOptimistic = false;\n      },\n      onError,\n      onFail: (failureCount, error) => {\n        this.dispatch({\n          type: 'failed',\n          failureCount,\n          error\n        });\n      },\n      onPause: () => {\n        this.dispatch({\n          type: 'pause'\n        });\n      },\n      onContinue: () => {\n        this.dispatch({\n          type: 'continue'\n        });\n      },\n      retry: context.options.retry,\n      retryDelay: context.options.retryDelay,\n      networkMode: context.options.networkMode\n    });\n    this.promise = this.retryer.promise;\n    return this.promise;\n  }\n\n  dispatch(action) {\n    const reducer = state => {\n      var _action$meta, _action$dataUpdatedAt;\n\n      switch (action.type) {\n        case 'failed':\n          return { ...state,\n            fetchFailureCount: action.failureCount,\n            fetchFailureReason: action.error\n          };\n\n        case 'pause':\n          return { ...state,\n            fetchStatus: 'paused'\n          };\n\n        case 'continue':\n          return { ...state,\n            fetchStatus: 'fetching'\n          };\n\n        case 'fetch':\n          return { ...state,\n            fetchFailureCount: 0,\n            fetchFailureReason: null,\n            fetchMeta: (_action$meta = action.meta) != null ? _action$meta : null,\n            fetchStatus: (0,_retryer_mjs__WEBPACK_IMPORTED_MODULE_3__.canFetch)(this.options.networkMode) ? 'fetching' : 'paused',\n            ...(!state.dataUpdatedAt && {\n              error: null,\n              status: 'loading'\n            })\n          };\n\n        case 'success':\n          return { ...state,\n            data: action.data,\n            dataUpdateCount: state.dataUpdateCount + 1,\n            dataUpdatedAt: (_action$dataUpdatedAt = action.dataUpdatedAt) != null ? _action$dataUpdatedAt : Date.now(),\n            error: null,\n            isInvalidated: false,\n            status: 'success',\n            ...(!action.manual && {\n              fetchStatus: 'idle',\n              fetchFailureCount: 0,\n              fetchFailureReason: null\n            })\n          };\n\n        case 'error':\n          const error = action.error;\n\n          if ((0,_retryer_mjs__WEBPACK_IMPORTED_MODULE_3__.isCancelledError)(error) && error.revert && this.revertState) {\n            return { ...this.revertState,\n              fetchStatus: 'idle'\n            };\n          }\n\n          return { ...state,\n            error: error,\n            errorUpdateCount: state.errorUpdateCount + 1,\n            errorUpdatedAt: Date.now(),\n            fetchFailureCount: state.fetchFailureCount + 1,\n            fetchFailureReason: error,\n            fetchStatus: 'idle',\n            status: 'error'\n          };\n\n        case 'invalidate':\n          return { ...state,\n            isInvalidated: true\n          };\n\n        case 'setState':\n          return { ...state,\n            ...action.state\n          };\n      }\n    };\n\n    this.state = reducer(this.state);\n    _notifyManager_mjs__WEBPACK_IMPORTED_MODULE_4__.notifyManager.batch(() => {\n      this.observers.forEach(observer => {\n        observer.onQueryUpdate(action);\n      });\n      this.cache.notify({\n        query: this,\n        type: 'updated',\n        action\n      });\n    });\n  }\n\n}\n\nfunction getDefaultState(options) {\n  const data = typeof options.initialData === 'function' ? options.initialData() : options.initialData;\n  const hasData = typeof data !== 'undefined';\n  const initialDataUpdatedAt = hasData ? typeof options.initialDataUpdatedAt === 'function' ? options.initialDataUpdatedAt() : options.initialDataUpdatedAt : 0;\n  return {\n    data,\n    dataUpdateCount: 0,\n    dataUpdatedAt: hasData ? initialDataUpdatedAt != null ? initialDataUpdatedAt : Date.now() : 0,\n    error: null,\n    errorUpdateCount: 0,\n    errorUpdatedAt: 0,\n    fetchFailureCount: 0,\n    fetchFailureReason: null,\n    fetchMeta: null,\n    isInvalidated: false,\n    status: hasData ? 'success' : 'loading',\n    fetchStatus: 'idle'\n  };\n}\n\n\n//# sourceMappingURL=query.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/lib/query.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/lib/queryCache.mjs":
/*!********************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/lib/queryCache.mjs ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryCache: () => (/* binding */ QueryCache)\n/* harmony export */ });\n/* harmony import */ var _utils_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/utils.mjs\");\n/* harmony import */ var _query_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./query.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/query.mjs\");\n/* harmony import */ var _notifyManager_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./notifyManager.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/notifyManager.mjs\");\n/* harmony import */ var _subscribable_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./subscribable.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/subscribable.mjs\");\n\n\n\n\n\n// CLASS\nclass QueryCache extends _subscribable_mjs__WEBPACK_IMPORTED_MODULE_0__.Subscribable {\n  constructor(config) {\n    super();\n    this.config = config || {};\n    this.queries = [];\n    this.queriesMap = {};\n  }\n\n  build(client, options, state) {\n    var _options$queryHash;\n\n    const queryKey = options.queryKey;\n    const queryHash = (_options$queryHash = options.queryHash) != null ? _options$queryHash : (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_1__.hashQueryKeyByOptions)(queryKey, options);\n    let query = this.get(queryHash);\n\n    if (!query) {\n      query = new _query_mjs__WEBPACK_IMPORTED_MODULE_2__.Query({\n        cache: this,\n        logger: client.getLogger(),\n        queryKey,\n        queryHash,\n        options: client.defaultQueryOptions(options),\n        state,\n        defaultOptions: client.getQueryDefaults(queryKey)\n      });\n      this.add(query);\n    }\n\n    return query;\n  }\n\n  add(query) {\n    if (!this.queriesMap[query.queryHash]) {\n      this.queriesMap[query.queryHash] = query;\n      this.queries.push(query);\n      this.notify({\n        type: 'added',\n        query\n      });\n    }\n  }\n\n  remove(query) {\n    const queryInMap = this.queriesMap[query.queryHash];\n\n    if (queryInMap) {\n      query.destroy();\n      this.queries = this.queries.filter(x => x !== query);\n\n      if (queryInMap === query) {\n        delete this.queriesMap[query.queryHash];\n      }\n\n      this.notify({\n        type: 'removed',\n        query\n      });\n    }\n  }\n\n  clear() {\n    _notifyManager_mjs__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(() => {\n      this.queries.forEach(query => {\n        this.remove(query);\n      });\n    });\n  }\n\n  get(queryHash) {\n    return this.queriesMap[queryHash];\n  }\n\n  getAll() {\n    return this.queries;\n  }\n\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  find(arg1, arg2) {\n    const [filters] = (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_1__.parseFilterArgs)(arg1, arg2);\n\n    if (typeof filters.exact === 'undefined') {\n      filters.exact = true;\n    }\n\n    return this.queries.find(query => (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_1__.matchQuery)(filters, query));\n  }\n\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  findAll(arg1, arg2) {\n    const [filters] = (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_1__.parseFilterArgs)(arg1, arg2);\n    return Object.keys(filters).length > 0 ? this.queries.filter(query => (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_1__.matchQuery)(filters, query)) : this.queries;\n  }\n\n  notify(event) {\n    _notifyManager_mjs__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(() => {\n      this.listeners.forEach(({\n        listener\n      }) => {\n        listener(event);\n      });\n    });\n  }\n\n  onFocus() {\n    _notifyManager_mjs__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(() => {\n      this.queries.forEach(query => {\n        query.onFocus();\n      });\n    });\n  }\n\n  onOnline() {\n    _notifyManager_mjs__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(() => {\n      this.queries.forEach(query => {\n        query.onOnline();\n      });\n    });\n  }\n\n}\n\n\n//# sourceMappingURL=queryCache.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/lib/queryCache.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/lib/queryClient.mjs":
/*!*********************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/lib/queryClient.mjs ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryClient: () => (/* binding */ QueryClient)\n/* harmony export */ });\n/* harmony import */ var _utils_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./utils.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/utils.mjs\");\n/* harmony import */ var _queryCache_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./queryCache.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/queryCache.mjs\");\n/* harmony import */ var _mutationCache_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./mutationCache.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/mutationCache.mjs\");\n/* harmony import */ var _focusManager_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./focusManager.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/focusManager.mjs\");\n/* harmony import */ var _onlineManager_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./onlineManager.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/onlineManager.mjs\");\n/* harmony import */ var _notifyManager_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./notifyManager.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/notifyManager.mjs\");\n/* harmony import */ var _infiniteQueryBehavior_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./infiniteQueryBehavior.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/infiniteQueryBehavior.mjs\");\n/* harmony import */ var _logger_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./logger.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/logger.mjs\");\n\n\n\n\n\n\n\n\n\n// CLASS\nclass QueryClient {\n  constructor(config = {}) {\n    this.queryCache = config.queryCache || new _queryCache_mjs__WEBPACK_IMPORTED_MODULE_0__.QueryCache();\n    this.mutationCache = config.mutationCache || new _mutationCache_mjs__WEBPACK_IMPORTED_MODULE_1__.MutationCache();\n    this.logger = config.logger || _logger_mjs__WEBPACK_IMPORTED_MODULE_2__.defaultLogger;\n    this.defaultOptions = config.defaultOptions || {};\n    this.queryDefaults = [];\n    this.mutationDefaults = [];\n    this.mountCount = 0;\n\n    if ( true && config.logger) {\n      this.logger.error(\"Passing a custom logger has been deprecated and will be removed in the next major version.\");\n    }\n  }\n\n  mount() {\n    this.mountCount++;\n    if (this.mountCount !== 1) return;\n    this.unsubscribeFocus = _focusManager_mjs__WEBPACK_IMPORTED_MODULE_3__.focusManager.subscribe(() => {\n      if (_focusManager_mjs__WEBPACK_IMPORTED_MODULE_3__.focusManager.isFocused()) {\n        this.resumePausedMutations();\n        this.queryCache.onFocus();\n      }\n    });\n    this.unsubscribeOnline = _onlineManager_mjs__WEBPACK_IMPORTED_MODULE_4__.onlineManager.subscribe(() => {\n      if (_onlineManager_mjs__WEBPACK_IMPORTED_MODULE_4__.onlineManager.isOnline()) {\n        this.resumePausedMutations();\n        this.queryCache.onOnline();\n      }\n    });\n  }\n\n  unmount() {\n    var _this$unsubscribeFocu, _this$unsubscribeOnli;\n\n    this.mountCount--;\n    if (this.mountCount !== 0) return;\n    (_this$unsubscribeFocu = this.unsubscribeFocus) == null ? void 0 : _this$unsubscribeFocu.call(this);\n    this.unsubscribeFocus = undefined;\n    (_this$unsubscribeOnli = this.unsubscribeOnline) == null ? void 0 : _this$unsubscribeOnli.call(this);\n    this.unsubscribeOnline = undefined;\n  }\n\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  isFetching(arg1, arg2) {\n    const [filters] = (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_5__.parseFilterArgs)(arg1, arg2);\n    filters.fetchStatus = 'fetching';\n    return this.queryCache.findAll(filters).length;\n  }\n\n  isMutating(filters) {\n    return this.mutationCache.findAll({ ...filters,\n      fetching: true\n    }).length;\n  }\n\n  /**\n   * @deprecated This method will accept only queryKey in the next major version.\n   */\n  getQueryData(queryKey, filters) {\n    var _this$queryCache$find;\n\n    return (_this$queryCache$find = this.queryCache.find(queryKey, filters)) == null ? void 0 : _this$queryCache$find.state.data;\n  }\n\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  ensureQueryData(arg1, arg2, arg3) {\n    const parsedOptions = (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_5__.parseQueryArgs)(arg1, arg2, arg3);\n    const cachedData = this.getQueryData(parsedOptions.queryKey);\n    return cachedData ? Promise.resolve(cachedData) : this.fetchQuery(parsedOptions);\n  }\n\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  getQueriesData(queryKeyOrFilters) {\n    return this.getQueryCache().findAll(queryKeyOrFilters).map(({\n      queryKey,\n      state\n    }) => {\n      const data = state.data;\n      return [queryKey, data];\n    });\n  }\n\n  setQueryData(queryKey, updater, options) {\n    const query = this.queryCache.find(queryKey);\n    const prevData = query == null ? void 0 : query.state.data;\n    const data = (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_5__.functionalUpdate)(updater, prevData);\n\n    if (typeof data === 'undefined') {\n      return undefined;\n    }\n\n    const parsedOptions = (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_5__.parseQueryArgs)(queryKey);\n    const defaultedOptions = this.defaultQueryOptions(parsedOptions);\n    return this.queryCache.build(this, defaultedOptions).setData(data, { ...options,\n      manual: true\n    });\n  }\n\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  setQueriesData(queryKeyOrFilters, updater, options) {\n    return _notifyManager_mjs__WEBPACK_IMPORTED_MODULE_6__.notifyManager.batch(() => this.getQueryCache().findAll(queryKeyOrFilters).map(({\n      queryKey\n    }) => [queryKey, this.setQueryData(queryKey, updater, options)]));\n  }\n\n  getQueryState(queryKey,\n  /**\n   * @deprecated This filters will be removed in the next major version.\n   */\n  filters) {\n    var _this$queryCache$find2;\n\n    return (_this$queryCache$find2 = this.queryCache.find(queryKey, filters)) == null ? void 0 : _this$queryCache$find2.state;\n  }\n\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  removeQueries(arg1, arg2) {\n    const [filters] = (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_5__.parseFilterArgs)(arg1, arg2);\n    const queryCache = this.queryCache;\n    _notifyManager_mjs__WEBPACK_IMPORTED_MODULE_6__.notifyManager.batch(() => {\n      queryCache.findAll(filters).forEach(query => {\n        queryCache.remove(query);\n      });\n    });\n  }\n\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  resetQueries(arg1, arg2, arg3) {\n    const [filters, options] = (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_5__.parseFilterArgs)(arg1, arg2, arg3);\n    const queryCache = this.queryCache;\n    const refetchFilters = {\n      type: 'active',\n      ...filters\n    };\n    return _notifyManager_mjs__WEBPACK_IMPORTED_MODULE_6__.notifyManager.batch(() => {\n      queryCache.findAll(filters).forEach(query => {\n        query.reset();\n      });\n      return this.refetchQueries(refetchFilters, options);\n    });\n  }\n\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  cancelQueries(arg1, arg2, arg3) {\n    const [filters, cancelOptions = {}] = (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_5__.parseFilterArgs)(arg1, arg2, arg3);\n\n    if (typeof cancelOptions.revert === 'undefined') {\n      cancelOptions.revert = true;\n    }\n\n    const promises = _notifyManager_mjs__WEBPACK_IMPORTED_MODULE_6__.notifyManager.batch(() => this.queryCache.findAll(filters).map(query => query.cancel(cancelOptions)));\n    return Promise.all(promises).then(_utils_mjs__WEBPACK_IMPORTED_MODULE_5__.noop).catch(_utils_mjs__WEBPACK_IMPORTED_MODULE_5__.noop);\n  }\n\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  invalidateQueries(arg1, arg2, arg3) {\n    const [filters, options] = (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_5__.parseFilterArgs)(arg1, arg2, arg3);\n    return _notifyManager_mjs__WEBPACK_IMPORTED_MODULE_6__.notifyManager.batch(() => {\n      var _ref, _filters$refetchType;\n\n      this.queryCache.findAll(filters).forEach(query => {\n        query.invalidate();\n      });\n\n      if (filters.refetchType === 'none') {\n        return Promise.resolve();\n      }\n\n      const refetchFilters = { ...filters,\n        type: (_ref = (_filters$refetchType = filters.refetchType) != null ? _filters$refetchType : filters.type) != null ? _ref : 'active'\n      };\n      return this.refetchQueries(refetchFilters, options);\n    });\n  }\n\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  refetchQueries(arg1, arg2, arg3) {\n    const [filters, options] = (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_5__.parseFilterArgs)(arg1, arg2, arg3);\n    const promises = _notifyManager_mjs__WEBPACK_IMPORTED_MODULE_6__.notifyManager.batch(() => this.queryCache.findAll(filters).filter(query => !query.isDisabled()).map(query => {\n      var _options$cancelRefetc;\n\n      return query.fetch(undefined, { ...options,\n        cancelRefetch: (_options$cancelRefetc = options == null ? void 0 : options.cancelRefetch) != null ? _options$cancelRefetc : true,\n        meta: {\n          refetchPage: filters.refetchPage\n        }\n      });\n    }));\n    let promise = Promise.all(promises).then(_utils_mjs__WEBPACK_IMPORTED_MODULE_5__.noop);\n\n    if (!(options != null && options.throwOnError)) {\n      promise = promise.catch(_utils_mjs__WEBPACK_IMPORTED_MODULE_5__.noop);\n    }\n\n    return promise;\n  }\n\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  fetchQuery(arg1, arg2, arg3) {\n    const parsedOptions = (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_5__.parseQueryArgs)(arg1, arg2, arg3);\n    const defaultedOptions = this.defaultQueryOptions(parsedOptions); // https://github.com/tannerlinsley/react-query/issues/652\n\n    if (typeof defaultedOptions.retry === 'undefined') {\n      defaultedOptions.retry = false;\n    }\n\n    const query = this.queryCache.build(this, defaultedOptions);\n    return query.isStaleByTime(defaultedOptions.staleTime) ? query.fetch(defaultedOptions) : Promise.resolve(query.state.data);\n  }\n\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  prefetchQuery(arg1, arg2, arg3) {\n    return this.fetchQuery(arg1, arg2, arg3).then(_utils_mjs__WEBPACK_IMPORTED_MODULE_5__.noop).catch(_utils_mjs__WEBPACK_IMPORTED_MODULE_5__.noop);\n  }\n\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  fetchInfiniteQuery(arg1, arg2, arg3) {\n    const parsedOptions = (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_5__.parseQueryArgs)(arg1, arg2, arg3);\n    parsedOptions.behavior = (0,_infiniteQueryBehavior_mjs__WEBPACK_IMPORTED_MODULE_7__.infiniteQueryBehavior)();\n    return this.fetchQuery(parsedOptions);\n  }\n\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  prefetchInfiniteQuery(arg1, arg2, arg3) {\n    return this.fetchInfiniteQuery(arg1, arg2, arg3).then(_utils_mjs__WEBPACK_IMPORTED_MODULE_5__.noop).catch(_utils_mjs__WEBPACK_IMPORTED_MODULE_5__.noop);\n  }\n\n  resumePausedMutations() {\n    return this.mutationCache.resumePausedMutations();\n  }\n\n  getQueryCache() {\n    return this.queryCache;\n  }\n\n  getMutationCache() {\n    return this.mutationCache;\n  }\n\n  getLogger() {\n    return this.logger;\n  }\n\n  getDefaultOptions() {\n    return this.defaultOptions;\n  }\n\n  setDefaultOptions(options) {\n    this.defaultOptions = options;\n  }\n\n  setQueryDefaults(queryKey, options) {\n    const result = this.queryDefaults.find(x => (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_5__.hashQueryKey)(queryKey) === (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_5__.hashQueryKey)(x.queryKey));\n\n    if (result) {\n      result.defaultOptions = options;\n    } else {\n      this.queryDefaults.push({\n        queryKey,\n        defaultOptions: options\n      });\n    }\n  }\n\n  getQueryDefaults(queryKey) {\n    if (!queryKey) {\n      return undefined;\n    } // Get the first matching defaults\n\n\n    const firstMatchingDefaults = this.queryDefaults.find(x => (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_5__.partialMatchKey)(queryKey, x.queryKey)); // Additional checks and error in dev mode\n\n    if (true) {\n      // Retrieve all matching defaults for the given key\n      const matchingDefaults = this.queryDefaults.filter(x => (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_5__.partialMatchKey)(queryKey, x.queryKey)); // It is ok not having defaults, but it is error prone to have more than 1 default for a given key\n\n      if (matchingDefaults.length > 1) {\n        this.logger.error(\"[QueryClient] Several query defaults match with key '\" + JSON.stringify(queryKey) + \"'. The first matching query defaults are used. Please check how query defaults are registered. Order does matter here. cf. https://react-query.tanstack.com/reference/QueryClient#queryclientsetquerydefaults.\");\n      }\n    }\n\n    return firstMatchingDefaults == null ? void 0 : firstMatchingDefaults.defaultOptions;\n  }\n\n  setMutationDefaults(mutationKey, options) {\n    const result = this.mutationDefaults.find(x => (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_5__.hashQueryKey)(mutationKey) === (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_5__.hashQueryKey)(x.mutationKey));\n\n    if (result) {\n      result.defaultOptions = options;\n    } else {\n      this.mutationDefaults.push({\n        mutationKey,\n        defaultOptions: options\n      });\n    }\n  }\n\n  getMutationDefaults(mutationKey) {\n    if (!mutationKey) {\n      return undefined;\n    } // Get the first matching defaults\n\n\n    const firstMatchingDefaults = this.mutationDefaults.find(x => (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_5__.partialMatchKey)(mutationKey, x.mutationKey)); // Additional checks and error in dev mode\n\n    if (true) {\n      // Retrieve all matching defaults for the given key\n      const matchingDefaults = this.mutationDefaults.filter(x => (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_5__.partialMatchKey)(mutationKey, x.mutationKey)); // It is ok not having defaults, but it is error prone to have more than 1 default for a given key\n\n      if (matchingDefaults.length > 1) {\n        this.logger.error(\"[QueryClient] Several mutation defaults match with key '\" + JSON.stringify(mutationKey) + \"'. The first matching mutation defaults are used. Please check how mutation defaults are registered. Order does matter here. cf. https://react-query.tanstack.com/reference/QueryClient#queryclientsetmutationdefaults.\");\n      }\n    }\n\n    return firstMatchingDefaults == null ? void 0 : firstMatchingDefaults.defaultOptions;\n  }\n\n  defaultQueryOptions(options) {\n    if (options != null && options._defaulted) {\n      return options;\n    }\n\n    const defaultedOptions = { ...this.defaultOptions.queries,\n      ...this.getQueryDefaults(options == null ? void 0 : options.queryKey),\n      ...options,\n      _defaulted: true\n    };\n\n    if (!defaultedOptions.queryHash && defaultedOptions.queryKey) {\n      defaultedOptions.queryHash = (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_5__.hashQueryKeyByOptions)(defaultedOptions.queryKey, defaultedOptions);\n    } // dependent default values\n\n\n    if (typeof defaultedOptions.refetchOnReconnect === 'undefined') {\n      defaultedOptions.refetchOnReconnect = defaultedOptions.networkMode !== 'always';\n    }\n\n    if (typeof defaultedOptions.useErrorBoundary === 'undefined') {\n      defaultedOptions.useErrorBoundary = !!defaultedOptions.suspense;\n    }\n\n    return defaultedOptions;\n  }\n\n  defaultMutationOptions(options) {\n    if (options != null && options._defaulted) {\n      return options;\n    }\n\n    return { ...this.defaultOptions.mutations,\n      ...this.getMutationDefaults(options == null ? void 0 : options.mutationKey),\n      ...options,\n      _defaulted: true\n    };\n  }\n\n  clear() {\n    this.queryCache.clear();\n    this.mutationCache.clear();\n  }\n\n}\n\n\n//# sourceMappingURL=queryClient.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/lib/queryClient.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/lib/queryObserver.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/lib/queryObserver.mjs ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryObserver: () => (/* binding */ QueryObserver)\n/* harmony export */ });\n/* harmony import */ var _utils_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/utils.mjs\");\n/* harmony import */ var _notifyManager_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./notifyManager.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/notifyManager.mjs\");\n/* harmony import */ var _focusManager_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./focusManager.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/focusManager.mjs\");\n/* harmony import */ var _subscribable_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./subscribable.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/subscribable.mjs\");\n/* harmony import */ var _retryer_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./retryer.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/retryer.mjs\");\n\n\n\n\n\n\nclass QueryObserver extends _subscribable_mjs__WEBPACK_IMPORTED_MODULE_0__.Subscribable {\n  constructor(client, options) {\n    super();\n    this.client = client;\n    this.options = options;\n    this.trackedProps = new Set();\n    this.selectError = null;\n    this.bindMethods();\n    this.setOptions(options);\n  }\n\n  bindMethods() {\n    this.remove = this.remove.bind(this);\n    this.refetch = this.refetch.bind(this);\n  }\n\n  onSubscribe() {\n    if (this.listeners.size === 1) {\n      this.currentQuery.addObserver(this);\n\n      if (shouldFetchOnMount(this.currentQuery, this.options)) {\n        this.executeFetch();\n      }\n\n      this.updateTimers();\n    }\n  }\n\n  onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.destroy();\n    }\n  }\n\n  shouldFetchOnReconnect() {\n    return shouldFetchOn(this.currentQuery, this.options, this.options.refetchOnReconnect);\n  }\n\n  shouldFetchOnWindowFocus() {\n    return shouldFetchOn(this.currentQuery, this.options, this.options.refetchOnWindowFocus);\n  }\n\n  destroy() {\n    this.listeners = new Set();\n    this.clearStaleTimeout();\n    this.clearRefetchInterval();\n    this.currentQuery.removeObserver(this);\n  }\n\n  setOptions(options, notifyOptions) {\n    const prevOptions = this.options;\n    const prevQuery = this.currentQuery;\n    this.options = this.client.defaultQueryOptions(options);\n\n    if ( true && typeof (options == null ? void 0 : options.isDataEqual) !== 'undefined') {\n      this.client.getLogger().error(\"The isDataEqual option has been deprecated and will be removed in the next major version. You can achieve the same functionality by passing a function as the structuralSharing option\");\n    }\n\n    if (!(0,_utils_mjs__WEBPACK_IMPORTED_MODULE_1__.shallowEqualObjects)(prevOptions, this.options)) {\n      this.client.getQueryCache().notify({\n        type: 'observerOptionsUpdated',\n        query: this.currentQuery,\n        observer: this\n      });\n    }\n\n    if (typeof this.options.enabled !== 'undefined' && typeof this.options.enabled !== 'boolean') {\n      throw new Error('Expected enabled to be a boolean');\n    } // Keep previous query key if the user does not supply one\n\n\n    if (!this.options.queryKey) {\n      this.options.queryKey = prevOptions.queryKey;\n    }\n\n    this.updateQuery();\n    const mounted = this.hasListeners(); // Fetch if there are subscribers\n\n    if (mounted && shouldFetchOptionally(this.currentQuery, prevQuery, this.options, prevOptions)) {\n      this.executeFetch();\n    } // Update result\n\n\n    this.updateResult(notifyOptions); // Update stale interval if needed\n\n    if (mounted && (this.currentQuery !== prevQuery || this.options.enabled !== prevOptions.enabled || this.options.staleTime !== prevOptions.staleTime)) {\n      this.updateStaleTimeout();\n    }\n\n    const nextRefetchInterval = this.computeRefetchInterval(); // Update refetch interval if needed\n\n    if (mounted && (this.currentQuery !== prevQuery || this.options.enabled !== prevOptions.enabled || nextRefetchInterval !== this.currentRefetchInterval)) {\n      this.updateRefetchInterval(nextRefetchInterval);\n    }\n  }\n\n  getOptimisticResult(options) {\n    const query = this.client.getQueryCache().build(this.client, options);\n    const result = this.createResult(query, options);\n\n    if (shouldAssignObserverCurrentProperties(this, result, options)) {\n      // this assigns the optimistic result to the current Observer\n      // because if the query function changes, useQuery will be performing\n      // an effect where it would fetch again.\n      // When the fetch finishes, we perform a deep data cloning in order\n      // to reuse objects references. This deep data clone is performed against\n      // the `observer.currentResult.data` property\n      // When QueryKey changes, we refresh the query and get new `optimistic`\n      // result, while we leave the `observer.currentResult`, so when new data\n      // arrives, it finds the old `observer.currentResult` which is related\n      // to the old QueryKey. Which means that currentResult and selectData are\n      // out of sync already.\n      // To solve this, we move the cursor of the currentResult everytime\n      // an observer reads an optimistic value.\n      // When keeping the previous data, the result doesn't change until new\n      // data arrives.\n      this.currentResult = result;\n      this.currentResultOptions = this.options;\n      this.currentResultState = this.currentQuery.state;\n    }\n\n    return result;\n  }\n\n  getCurrentResult() {\n    return this.currentResult;\n  }\n\n  trackResult(result) {\n    const trackedResult = {};\n    Object.keys(result).forEach(key => {\n      Object.defineProperty(trackedResult, key, {\n        configurable: false,\n        enumerable: true,\n        get: () => {\n          this.trackedProps.add(key);\n          return result[key];\n        }\n      });\n    });\n    return trackedResult;\n  }\n\n  getCurrentQuery() {\n    return this.currentQuery;\n  }\n\n  remove() {\n    this.client.getQueryCache().remove(this.currentQuery);\n  }\n\n  refetch({\n    refetchPage,\n    ...options\n  } = {}) {\n    return this.fetch({ ...options,\n      meta: {\n        refetchPage\n      }\n    });\n  }\n\n  fetchOptimistic(options) {\n    const defaultedOptions = this.client.defaultQueryOptions(options);\n    const query = this.client.getQueryCache().build(this.client, defaultedOptions);\n    query.isFetchingOptimistic = true;\n    return query.fetch().then(() => this.createResult(query, defaultedOptions));\n  }\n\n  fetch(fetchOptions) {\n    var _fetchOptions$cancelR;\n\n    return this.executeFetch({ ...fetchOptions,\n      cancelRefetch: (_fetchOptions$cancelR = fetchOptions.cancelRefetch) != null ? _fetchOptions$cancelR : true\n    }).then(() => {\n      this.updateResult();\n      return this.currentResult;\n    });\n  }\n\n  executeFetch(fetchOptions) {\n    // Make sure we reference the latest query as the current one might have been removed\n    this.updateQuery(); // Fetch\n\n    let promise = this.currentQuery.fetch(this.options, fetchOptions);\n\n    if (!(fetchOptions != null && fetchOptions.throwOnError)) {\n      promise = promise.catch(_utils_mjs__WEBPACK_IMPORTED_MODULE_1__.noop);\n    }\n\n    return promise;\n  }\n\n  updateStaleTimeout() {\n    this.clearStaleTimeout();\n\n    if (_utils_mjs__WEBPACK_IMPORTED_MODULE_1__.isServer || this.currentResult.isStale || !(0,_utils_mjs__WEBPACK_IMPORTED_MODULE_1__.isValidTimeout)(this.options.staleTime)) {\n      return;\n    }\n\n    const time = (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_1__.timeUntilStale)(this.currentResult.dataUpdatedAt, this.options.staleTime); // The timeout is sometimes triggered 1 ms before the stale time expiration.\n    // To mitigate this issue we always add 1 ms to the timeout.\n\n    const timeout = time + 1;\n    this.staleTimeoutId = setTimeout(() => {\n      if (!this.currentResult.isStale) {\n        this.updateResult();\n      }\n    }, timeout);\n  }\n\n  computeRefetchInterval() {\n    var _this$options$refetch;\n\n    return typeof this.options.refetchInterval === 'function' ? this.options.refetchInterval(this.currentResult.data, this.currentQuery) : (_this$options$refetch = this.options.refetchInterval) != null ? _this$options$refetch : false;\n  }\n\n  updateRefetchInterval(nextInterval) {\n    this.clearRefetchInterval();\n    this.currentRefetchInterval = nextInterval;\n\n    if (_utils_mjs__WEBPACK_IMPORTED_MODULE_1__.isServer || this.options.enabled === false || !(0,_utils_mjs__WEBPACK_IMPORTED_MODULE_1__.isValidTimeout)(this.currentRefetchInterval) || this.currentRefetchInterval === 0) {\n      return;\n    }\n\n    this.refetchIntervalId = setInterval(() => {\n      if (this.options.refetchIntervalInBackground || _focusManager_mjs__WEBPACK_IMPORTED_MODULE_2__.focusManager.isFocused()) {\n        this.executeFetch();\n      }\n    }, this.currentRefetchInterval);\n  }\n\n  updateTimers() {\n    this.updateStaleTimeout();\n    this.updateRefetchInterval(this.computeRefetchInterval());\n  }\n\n  clearStaleTimeout() {\n    if (this.staleTimeoutId) {\n      clearTimeout(this.staleTimeoutId);\n      this.staleTimeoutId = undefined;\n    }\n  }\n\n  clearRefetchInterval() {\n    if (this.refetchIntervalId) {\n      clearInterval(this.refetchIntervalId);\n      this.refetchIntervalId = undefined;\n    }\n  }\n\n  createResult(query, options) {\n    const prevQuery = this.currentQuery;\n    const prevOptions = this.options;\n    const prevResult = this.currentResult;\n    const prevResultState = this.currentResultState;\n    const prevResultOptions = this.currentResultOptions;\n    const queryChange = query !== prevQuery;\n    const queryInitialState = queryChange ? query.state : this.currentQueryInitialState;\n    const prevQueryResult = queryChange ? this.currentResult : this.previousQueryResult;\n    const {\n      state\n    } = query;\n    let {\n      dataUpdatedAt,\n      error,\n      errorUpdatedAt,\n      fetchStatus,\n      status\n    } = state;\n    let isPreviousData = false;\n    let isPlaceholderData = false;\n    let data; // Optimistically set result in fetching state if needed\n\n    if (options._optimisticResults) {\n      const mounted = this.hasListeners();\n      const fetchOnMount = !mounted && shouldFetchOnMount(query, options);\n      const fetchOptionally = mounted && shouldFetchOptionally(query, prevQuery, options, prevOptions);\n\n      if (fetchOnMount || fetchOptionally) {\n        fetchStatus = (0,_retryer_mjs__WEBPACK_IMPORTED_MODULE_3__.canFetch)(query.options.networkMode) ? 'fetching' : 'paused';\n\n        if (!dataUpdatedAt) {\n          status = 'loading';\n        }\n      }\n\n      if (options._optimisticResults === 'isRestoring') {\n        fetchStatus = 'idle';\n      }\n    } // Keep previous data if needed\n\n\n    if (options.keepPreviousData && !state.dataUpdatedAt && prevQueryResult != null && prevQueryResult.isSuccess && status !== 'error') {\n      data = prevQueryResult.data;\n      dataUpdatedAt = prevQueryResult.dataUpdatedAt;\n      status = prevQueryResult.status;\n      isPreviousData = true;\n    } // Select data if needed\n    else if (options.select && typeof state.data !== 'undefined') {\n      // Memoize select result\n      if (prevResult && state.data === (prevResultState == null ? void 0 : prevResultState.data) && options.select === this.selectFn) {\n        data = this.selectResult;\n      } else {\n        try {\n          this.selectFn = options.select;\n          data = options.select(state.data);\n          data = (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_1__.replaceData)(prevResult == null ? void 0 : prevResult.data, data, options);\n          this.selectResult = data;\n          this.selectError = null;\n        } catch (selectError) {\n          if (true) {\n            this.client.getLogger().error(selectError);\n          }\n\n          this.selectError = selectError;\n        }\n      }\n    } // Use query data\n    else {\n      data = state.data;\n    } // Show placeholder data if needed\n\n\n    if (typeof options.placeholderData !== 'undefined' && typeof data === 'undefined' && status === 'loading') {\n      let placeholderData; // Memoize placeholder data\n\n      if (prevResult != null && prevResult.isPlaceholderData && options.placeholderData === (prevResultOptions == null ? void 0 : prevResultOptions.placeholderData)) {\n        placeholderData = prevResult.data;\n      } else {\n        placeholderData = typeof options.placeholderData === 'function' ? options.placeholderData() : options.placeholderData;\n\n        if (options.select && typeof placeholderData !== 'undefined') {\n          try {\n            placeholderData = options.select(placeholderData);\n            this.selectError = null;\n          } catch (selectError) {\n            if (true) {\n              this.client.getLogger().error(selectError);\n            }\n\n            this.selectError = selectError;\n          }\n        }\n      }\n\n      if (typeof placeholderData !== 'undefined') {\n        status = 'success';\n        data = (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_1__.replaceData)(prevResult == null ? void 0 : prevResult.data, placeholderData, options);\n        isPlaceholderData = true;\n      }\n    }\n\n    if (this.selectError) {\n      error = this.selectError;\n      data = this.selectResult;\n      errorUpdatedAt = Date.now();\n      status = 'error';\n    }\n\n    const isFetching = fetchStatus === 'fetching';\n    const isLoading = status === 'loading';\n    const isError = status === 'error';\n    const result = {\n      status,\n      fetchStatus,\n      isLoading,\n      isSuccess: status === 'success',\n      isError,\n      isInitialLoading: isLoading && isFetching,\n      data,\n      dataUpdatedAt,\n      error,\n      errorUpdatedAt,\n      failureCount: state.fetchFailureCount,\n      failureReason: state.fetchFailureReason,\n      errorUpdateCount: state.errorUpdateCount,\n      isFetched: state.dataUpdateCount > 0 || state.errorUpdateCount > 0,\n      isFetchedAfterMount: state.dataUpdateCount > queryInitialState.dataUpdateCount || state.errorUpdateCount > queryInitialState.errorUpdateCount,\n      isFetching,\n      isRefetching: isFetching && !isLoading,\n      isLoadingError: isError && state.dataUpdatedAt === 0,\n      isPaused: fetchStatus === 'paused',\n      isPlaceholderData,\n      isPreviousData,\n      isRefetchError: isError && state.dataUpdatedAt !== 0,\n      isStale: isStale(query, options),\n      refetch: this.refetch,\n      remove: this.remove\n    };\n    return result;\n  }\n\n  updateResult(notifyOptions) {\n    const prevResult = this.currentResult;\n    const nextResult = this.createResult(this.currentQuery, this.options);\n    this.currentResultState = this.currentQuery.state;\n    this.currentResultOptions = this.options; // Only notify and update result if something has changed\n\n    if ((0,_utils_mjs__WEBPACK_IMPORTED_MODULE_1__.shallowEqualObjects)(nextResult, prevResult)) {\n      return;\n    }\n\n    this.currentResult = nextResult; // Determine which callbacks to trigger\n\n    const defaultNotifyOptions = {\n      cache: true\n    };\n\n    const shouldNotifyListeners = () => {\n      if (!prevResult) {\n        return true;\n      }\n\n      const {\n        notifyOnChangeProps\n      } = this.options;\n      const notifyOnChangePropsValue = typeof notifyOnChangeProps === 'function' ? notifyOnChangeProps() : notifyOnChangeProps;\n\n      if (notifyOnChangePropsValue === 'all' || !notifyOnChangePropsValue && !this.trackedProps.size) {\n        return true;\n      }\n\n      const includedProps = new Set(notifyOnChangePropsValue != null ? notifyOnChangePropsValue : this.trackedProps);\n\n      if (this.options.useErrorBoundary) {\n        includedProps.add('error');\n      }\n\n      return Object.keys(this.currentResult).some(key => {\n        const typedKey = key;\n        const changed = this.currentResult[typedKey] !== prevResult[typedKey];\n        return changed && includedProps.has(typedKey);\n      });\n    };\n\n    if ((notifyOptions == null ? void 0 : notifyOptions.listeners) !== false && shouldNotifyListeners()) {\n      defaultNotifyOptions.listeners = true;\n    }\n\n    this.notify({ ...defaultNotifyOptions,\n      ...notifyOptions\n    });\n  }\n\n  updateQuery() {\n    const query = this.client.getQueryCache().build(this.client, this.options);\n\n    if (query === this.currentQuery) {\n      return;\n    }\n\n    const prevQuery = this.currentQuery;\n    this.currentQuery = query;\n    this.currentQueryInitialState = query.state;\n    this.previousQueryResult = this.currentResult;\n\n    if (this.hasListeners()) {\n      prevQuery == null ? void 0 : prevQuery.removeObserver(this);\n      query.addObserver(this);\n    }\n  }\n\n  onQueryUpdate(action) {\n    const notifyOptions = {};\n\n    if (action.type === 'success') {\n      notifyOptions.onSuccess = !action.manual;\n    } else if (action.type === 'error' && !(0,_retryer_mjs__WEBPACK_IMPORTED_MODULE_3__.isCancelledError)(action.error)) {\n      notifyOptions.onError = true;\n    }\n\n    this.updateResult(notifyOptions);\n\n    if (this.hasListeners()) {\n      this.updateTimers();\n    }\n  }\n\n  notify(notifyOptions) {\n    _notifyManager_mjs__WEBPACK_IMPORTED_MODULE_4__.notifyManager.batch(() => {\n      // First trigger the configuration callbacks\n      if (notifyOptions.onSuccess) {\n        var _this$options$onSucce, _this$options, _this$options$onSettl, _this$options2;\n\n        (_this$options$onSucce = (_this$options = this.options).onSuccess) == null ? void 0 : _this$options$onSucce.call(_this$options, this.currentResult.data);\n        (_this$options$onSettl = (_this$options2 = this.options).onSettled) == null ? void 0 : _this$options$onSettl.call(_this$options2, this.currentResult.data, null);\n      } else if (notifyOptions.onError) {\n        var _this$options$onError, _this$options3, _this$options$onSettl2, _this$options4;\n\n        (_this$options$onError = (_this$options3 = this.options).onError) == null ? void 0 : _this$options$onError.call(_this$options3, this.currentResult.error);\n        (_this$options$onSettl2 = (_this$options4 = this.options).onSettled) == null ? void 0 : _this$options$onSettl2.call(_this$options4, undefined, this.currentResult.error);\n      } // Then trigger the listeners\n\n\n      if (notifyOptions.listeners) {\n        this.listeners.forEach(({\n          listener\n        }) => {\n          listener(this.currentResult);\n        });\n      } // Then the cache listeners\n\n\n      if (notifyOptions.cache) {\n        this.client.getQueryCache().notify({\n          query: this.currentQuery,\n          type: 'observerResultsUpdated'\n        });\n      }\n    });\n  }\n\n}\n\nfunction shouldLoadOnMount(query, options) {\n  return options.enabled !== false && !query.state.dataUpdatedAt && !(query.state.status === 'error' && options.retryOnMount === false);\n}\n\nfunction shouldFetchOnMount(query, options) {\n  return shouldLoadOnMount(query, options) || query.state.dataUpdatedAt > 0 && shouldFetchOn(query, options, options.refetchOnMount);\n}\n\nfunction shouldFetchOn(query, options, field) {\n  if (options.enabled !== false) {\n    const value = typeof field === 'function' ? field(query) : field;\n    return value === 'always' || value !== false && isStale(query, options);\n  }\n\n  return false;\n}\n\nfunction shouldFetchOptionally(query, prevQuery, options, prevOptions) {\n  return options.enabled !== false && (query !== prevQuery || prevOptions.enabled === false) && (!options.suspense || query.state.status !== 'error') && isStale(query, options);\n}\n\nfunction isStale(query, options) {\n  return query.isStaleByTime(options.staleTime);\n} // this function would decide if we will update the observer's 'current'\n// properties after an optimistic reading via getOptimisticResult\n\n\nfunction shouldAssignObserverCurrentProperties(observer, optimisticResult, options) {\n  // it is important to keep this condition like this for three reasons:\n  // 1. It will get removed in the v5\n  // 2. it reads: don't update the properties if we want to keep the previous\n  // data.\n  // 3. The opposite condition (!options.keepPreviousData) would fallthrough\n  // and will result in a bad decision\n  if (options.keepPreviousData) {\n    return false;\n  } // this means we want to put some placeholder data when pending and queryKey\n  // changed.\n\n\n  if (options.placeholderData !== undefined) {\n    // re-assign properties only if current data is placeholder data\n    // which means that data did not arrive yet, so, if there is some cached data\n    // we need to \"prepare\" to receive it\n    return optimisticResult.isPlaceholderData;\n  } // if the newly created result isn't what the observer is holding as current,\n  // then we'll need to update the properties as well\n\n\n  if (!(0,_utils_mjs__WEBPACK_IMPORTED_MODULE_1__.shallowEqualObjects)(observer.getCurrentResult(), optimisticResult)) {\n    return true;\n  } // basically, just keep previous properties if nothing changed\n\n\n  return false;\n}\n\n\n//# sourceMappingURL=queryObserver.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRhbnN0YWNrL3F1ZXJ5LWNvcmUvYnVpbGQvbGliL3F1ZXJ5T2JzZXJ2ZXIubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUErRztBQUMzRDtBQUNGO0FBQ0E7QUFDUzs7QUFFM0QsNEJBQTRCLDJEQUFZO0FBQ3hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLFFBQVEsS0FBcUM7QUFDN0M7QUFDQTs7QUFFQSxTQUFTLCtEQUFtQjtBQUM1QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDs7QUFFQTtBQUNBO0FBQ0EsTUFBTTs7O0FBR047QUFDQTtBQUNBOztBQUVBO0FBQ0EseUNBQXlDOztBQUV6QztBQUNBO0FBQ0EsTUFBTTs7O0FBR04sc0NBQXNDOztBQUV0QztBQUNBO0FBQ0E7O0FBRUEsK0RBQStEOztBQUUvRDtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUCxLQUFLO0FBQ0w7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLElBQUksSUFBSTtBQUNSLHdCQUF3QjtBQUN4QjtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUEsK0JBQStCO0FBQy9CO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7O0FBRUE7QUFDQTtBQUNBLHdCQUF3Qjs7QUFFeEI7O0FBRUE7QUFDQSw4QkFBOEIsNENBQUk7QUFDbEM7O0FBRUE7QUFDQTs7QUFFQTtBQUNBOztBQUVBLFFBQVEsZ0RBQVEsbUNBQW1DLDBEQUFjO0FBQ2pFO0FBQ0E7O0FBRUEsaUJBQWlCLDBEQUFjLDREQUE0RDtBQUMzRjs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUEsUUFBUSxnREFBUSx1Q0FBdUMsMERBQWM7QUFDckU7QUFDQTs7QUFFQTtBQUNBLHNEQUFzRCwyREFBWTtBQUNsRTtBQUNBO0FBQ0EsS0FBSztBQUNMOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBLGNBQWM7O0FBRWQ7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxzQkFBc0Isc0RBQVE7O0FBRTlCO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLE1BQU07OztBQUdOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCLHVEQUFXO0FBQzVCO0FBQ0E7QUFDQSxVQUFVO0FBQ1YsY0FBYyxJQUFxQztBQUNuRDtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0EsTUFBTTs7O0FBR047QUFDQSwyQkFBMkI7O0FBRTNCO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1osZ0JBQWdCLElBQXFDO0FBQ3JEO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLGVBQWUsdURBQVc7QUFDMUI7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOENBQThDOztBQUU5QyxRQUFRLCtEQUFtQjtBQUMzQjtBQUNBOztBQUVBLHFDQUFxQzs7QUFFckM7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7O0FBRUE7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDs7QUFFQTtBQUNBO0FBQ0E7O0FBRUEsa0JBQWtCO0FBQ2xCO0FBQ0EsS0FBSztBQUNMOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsTUFBTSxxQ0FBcUMsOERBQWdCO0FBQzNEO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxJQUFJLDZEQUFhO0FBQ2pCO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsUUFBUTtBQUNSOztBQUVBO0FBQ0E7QUFDQSxRQUFROzs7QUFHUjtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQSxTQUFTO0FBQ1QsUUFBUTs7O0FBR1I7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQSxLQUFLO0FBQ0w7O0FBRUE7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxFQUFFO0FBQ0Y7OztBQUdBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjs7O0FBR0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjs7O0FBR0EsT0FBTywrREFBbUI7QUFDMUI7QUFDQSxJQUFJOzs7QUFHSjtBQUNBOztBQUV5QjtBQUN6QiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxiZXJrYVxcRGVza3RvcFxcUkVORVJPXFxuZXh0X3JlbmVyb1xcbm9kZV9tb2R1bGVzXFxAdGFuc3RhY2tcXHF1ZXJ5LWNvcmVcXGJ1aWxkXFxsaWJcXHF1ZXJ5T2JzZXJ2ZXIubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHNoYWxsb3dFcXVhbE9iamVjdHMsIG5vb3AsIGlzU2VydmVyLCBpc1ZhbGlkVGltZW91dCwgdGltZVVudGlsU3RhbGUsIHJlcGxhY2VEYXRhIH0gZnJvbSAnLi91dGlscy5tanMnO1xuaW1wb3J0IHsgbm90aWZ5TWFuYWdlciB9IGZyb20gJy4vbm90aWZ5TWFuYWdlci5tanMnO1xuaW1wb3J0IHsgZm9jdXNNYW5hZ2VyIH0gZnJvbSAnLi9mb2N1c01hbmFnZXIubWpzJztcbmltcG9ydCB7IFN1YnNjcmliYWJsZSB9IGZyb20gJy4vc3Vic2NyaWJhYmxlLm1qcyc7XG5pbXBvcnQgeyBjYW5GZXRjaCwgaXNDYW5jZWxsZWRFcnJvciB9IGZyb20gJy4vcmV0cnllci5tanMnO1xuXG5jbGFzcyBRdWVyeU9ic2VydmVyIGV4dGVuZHMgU3Vic2NyaWJhYmxlIHtcbiAgY29uc3RydWN0b3IoY2xpZW50LCBvcHRpb25zKSB7XG4gICAgc3VwZXIoKTtcbiAgICB0aGlzLmNsaWVudCA9IGNsaWVudDtcbiAgICB0aGlzLm9wdGlvbnMgPSBvcHRpb25zO1xuICAgIHRoaXMudHJhY2tlZFByb3BzID0gbmV3IFNldCgpO1xuICAgIHRoaXMuc2VsZWN0RXJyb3IgPSBudWxsO1xuICAgIHRoaXMuYmluZE1ldGhvZHMoKTtcbiAgICB0aGlzLnNldE9wdGlvbnMob3B0aW9ucyk7XG4gIH1cblxuICBiaW5kTWV0aG9kcygpIHtcbiAgICB0aGlzLnJlbW92ZSA9IHRoaXMucmVtb3ZlLmJpbmQodGhpcyk7XG4gICAgdGhpcy5yZWZldGNoID0gdGhpcy5yZWZldGNoLmJpbmQodGhpcyk7XG4gIH1cblxuICBvblN1YnNjcmliZSgpIHtcbiAgICBpZiAodGhpcy5saXN0ZW5lcnMuc2l6ZSA9PT0gMSkge1xuICAgICAgdGhpcy5jdXJyZW50UXVlcnkuYWRkT2JzZXJ2ZXIodGhpcyk7XG5cbiAgICAgIGlmIChzaG91bGRGZXRjaE9uTW91bnQodGhpcy5jdXJyZW50UXVlcnksIHRoaXMub3B0aW9ucykpIHtcbiAgICAgICAgdGhpcy5leGVjdXRlRmV0Y2goKTtcbiAgICAgIH1cblxuICAgICAgdGhpcy51cGRhdGVUaW1lcnMoKTtcbiAgICB9XG4gIH1cblxuICBvblVuc3Vic2NyaWJlKCkge1xuICAgIGlmICghdGhpcy5oYXNMaXN0ZW5lcnMoKSkge1xuICAgICAgdGhpcy5kZXN0cm95KCk7XG4gICAgfVxuICB9XG5cbiAgc2hvdWxkRmV0Y2hPblJlY29ubmVjdCgpIHtcbiAgICByZXR1cm4gc2hvdWxkRmV0Y2hPbih0aGlzLmN1cnJlbnRRdWVyeSwgdGhpcy5vcHRpb25zLCB0aGlzLm9wdGlvbnMucmVmZXRjaE9uUmVjb25uZWN0KTtcbiAgfVxuXG4gIHNob3VsZEZldGNoT25XaW5kb3dGb2N1cygpIHtcbiAgICByZXR1cm4gc2hvdWxkRmV0Y2hPbih0aGlzLmN1cnJlbnRRdWVyeSwgdGhpcy5vcHRpb25zLCB0aGlzLm9wdGlvbnMucmVmZXRjaE9uV2luZG93Rm9jdXMpO1xuICB9XG5cbiAgZGVzdHJveSgpIHtcbiAgICB0aGlzLmxpc3RlbmVycyA9IG5ldyBTZXQoKTtcbiAgICB0aGlzLmNsZWFyU3RhbGVUaW1lb3V0KCk7XG4gICAgdGhpcy5jbGVhclJlZmV0Y2hJbnRlcnZhbCgpO1xuICAgIHRoaXMuY3VycmVudFF1ZXJ5LnJlbW92ZU9ic2VydmVyKHRoaXMpO1xuICB9XG5cbiAgc2V0T3B0aW9ucyhvcHRpb25zLCBub3RpZnlPcHRpb25zKSB7XG4gICAgY29uc3QgcHJldk9wdGlvbnMgPSB0aGlzLm9wdGlvbnM7XG4gICAgY29uc3QgcHJldlF1ZXJ5ID0gdGhpcy5jdXJyZW50UXVlcnk7XG4gICAgdGhpcy5vcHRpb25zID0gdGhpcy5jbGllbnQuZGVmYXVsdFF1ZXJ5T3B0aW9ucyhvcHRpb25zKTtcblxuICAgIGlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nICYmIHR5cGVvZiAob3B0aW9ucyA9PSBudWxsID8gdm9pZCAwIDogb3B0aW9ucy5pc0RhdGFFcXVhbCkgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICB0aGlzLmNsaWVudC5nZXRMb2dnZXIoKS5lcnJvcihcIlRoZSBpc0RhdGFFcXVhbCBvcHRpb24gaGFzIGJlZW4gZGVwcmVjYXRlZCBhbmQgd2lsbCBiZSByZW1vdmVkIGluIHRoZSBuZXh0IG1ham9yIHZlcnNpb24uIFlvdSBjYW4gYWNoaWV2ZSB0aGUgc2FtZSBmdW5jdGlvbmFsaXR5IGJ5IHBhc3NpbmcgYSBmdW5jdGlvbiBhcyB0aGUgc3RydWN0dXJhbFNoYXJpbmcgb3B0aW9uXCIpO1xuICAgIH1cblxuICAgIGlmICghc2hhbGxvd0VxdWFsT2JqZWN0cyhwcmV2T3B0aW9ucywgdGhpcy5vcHRpb25zKSkge1xuICAgICAgdGhpcy5jbGllbnQuZ2V0UXVlcnlDYWNoZSgpLm5vdGlmeSh7XG4gICAgICAgIHR5cGU6ICdvYnNlcnZlck9wdGlvbnNVcGRhdGVkJyxcbiAgICAgICAgcXVlcnk6IHRoaXMuY3VycmVudFF1ZXJ5LFxuICAgICAgICBvYnNlcnZlcjogdGhpc1xuICAgICAgfSk7XG4gICAgfVxuXG4gICAgaWYgKHR5cGVvZiB0aGlzLm9wdGlvbnMuZW5hYmxlZCAhPT0gJ3VuZGVmaW5lZCcgJiYgdHlwZW9mIHRoaXMub3B0aW9ucy5lbmFibGVkICE9PSAnYm9vbGVhbicpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcignRXhwZWN0ZWQgZW5hYmxlZCB0byBiZSBhIGJvb2xlYW4nKTtcbiAgICB9IC8vIEtlZXAgcHJldmlvdXMgcXVlcnkga2V5IGlmIHRoZSB1c2VyIGRvZXMgbm90IHN1cHBseSBvbmVcblxuXG4gICAgaWYgKCF0aGlzLm9wdGlvbnMucXVlcnlLZXkpIHtcbiAgICAgIHRoaXMub3B0aW9ucy5xdWVyeUtleSA9IHByZXZPcHRpb25zLnF1ZXJ5S2V5O1xuICAgIH1cblxuICAgIHRoaXMudXBkYXRlUXVlcnkoKTtcbiAgICBjb25zdCBtb3VudGVkID0gdGhpcy5oYXNMaXN0ZW5lcnMoKTsgLy8gRmV0Y2ggaWYgdGhlcmUgYXJlIHN1YnNjcmliZXJzXG5cbiAgICBpZiAobW91bnRlZCAmJiBzaG91bGRGZXRjaE9wdGlvbmFsbHkodGhpcy5jdXJyZW50UXVlcnksIHByZXZRdWVyeSwgdGhpcy5vcHRpb25zLCBwcmV2T3B0aW9ucykpIHtcbiAgICAgIHRoaXMuZXhlY3V0ZUZldGNoKCk7XG4gICAgfSAvLyBVcGRhdGUgcmVzdWx0XG5cblxuICAgIHRoaXMudXBkYXRlUmVzdWx0KG5vdGlmeU9wdGlvbnMpOyAvLyBVcGRhdGUgc3RhbGUgaW50ZXJ2YWwgaWYgbmVlZGVkXG5cbiAgICBpZiAobW91bnRlZCAmJiAodGhpcy5jdXJyZW50UXVlcnkgIT09IHByZXZRdWVyeSB8fCB0aGlzLm9wdGlvbnMuZW5hYmxlZCAhPT0gcHJldk9wdGlvbnMuZW5hYmxlZCB8fCB0aGlzLm9wdGlvbnMuc3RhbGVUaW1lICE9PSBwcmV2T3B0aW9ucy5zdGFsZVRpbWUpKSB7XG4gICAgICB0aGlzLnVwZGF0ZVN0YWxlVGltZW91dCgpO1xuICAgIH1cblxuICAgIGNvbnN0IG5leHRSZWZldGNoSW50ZXJ2YWwgPSB0aGlzLmNvbXB1dGVSZWZldGNoSW50ZXJ2YWwoKTsgLy8gVXBkYXRlIHJlZmV0Y2ggaW50ZXJ2YWwgaWYgbmVlZGVkXG5cbiAgICBpZiAobW91bnRlZCAmJiAodGhpcy5jdXJyZW50UXVlcnkgIT09IHByZXZRdWVyeSB8fCB0aGlzLm9wdGlvbnMuZW5hYmxlZCAhPT0gcHJldk9wdGlvbnMuZW5hYmxlZCB8fCBuZXh0UmVmZXRjaEludGVydmFsICE9PSB0aGlzLmN1cnJlbnRSZWZldGNoSW50ZXJ2YWwpKSB7XG4gICAgICB0aGlzLnVwZGF0ZVJlZmV0Y2hJbnRlcnZhbChuZXh0UmVmZXRjaEludGVydmFsKTtcbiAgICB9XG4gIH1cblxuICBnZXRPcHRpbWlzdGljUmVzdWx0KG9wdGlvbnMpIHtcbiAgICBjb25zdCBxdWVyeSA9IHRoaXMuY2xpZW50LmdldFF1ZXJ5Q2FjaGUoKS5idWlsZCh0aGlzLmNsaWVudCwgb3B0aW9ucyk7XG4gICAgY29uc3QgcmVzdWx0ID0gdGhpcy5jcmVhdGVSZXN1bHQocXVlcnksIG9wdGlvbnMpO1xuXG4gICAgaWYgKHNob3VsZEFzc2lnbk9ic2VydmVyQ3VycmVudFByb3BlcnRpZXModGhpcywgcmVzdWx0LCBvcHRpb25zKSkge1xuICAgICAgLy8gdGhpcyBhc3NpZ25zIHRoZSBvcHRpbWlzdGljIHJlc3VsdCB0byB0aGUgY3VycmVudCBPYnNlcnZlclxuICAgICAgLy8gYmVjYXVzZSBpZiB0aGUgcXVlcnkgZnVuY3Rpb24gY2hhbmdlcywgdXNlUXVlcnkgd2lsbCBiZSBwZXJmb3JtaW5nXG4gICAgICAvLyBhbiBlZmZlY3Qgd2hlcmUgaXQgd291bGQgZmV0Y2ggYWdhaW4uXG4gICAgICAvLyBXaGVuIHRoZSBmZXRjaCBmaW5pc2hlcywgd2UgcGVyZm9ybSBhIGRlZXAgZGF0YSBjbG9uaW5nIGluIG9yZGVyXG4gICAgICAvLyB0byByZXVzZSBvYmplY3RzIHJlZmVyZW5jZXMuIFRoaXMgZGVlcCBkYXRhIGNsb25lIGlzIHBlcmZvcm1lZCBhZ2FpbnN0XG4gICAgICAvLyB0aGUgYG9ic2VydmVyLmN1cnJlbnRSZXN1bHQuZGF0YWAgcHJvcGVydHlcbiAgICAgIC8vIFdoZW4gUXVlcnlLZXkgY2hhbmdlcywgd2UgcmVmcmVzaCB0aGUgcXVlcnkgYW5kIGdldCBuZXcgYG9wdGltaXN0aWNgXG4gICAgICAvLyByZXN1bHQsIHdoaWxlIHdlIGxlYXZlIHRoZSBgb2JzZXJ2ZXIuY3VycmVudFJlc3VsdGAsIHNvIHdoZW4gbmV3IGRhdGFcbiAgICAgIC8vIGFycml2ZXMsIGl0IGZpbmRzIHRoZSBvbGQgYG9ic2VydmVyLmN1cnJlbnRSZXN1bHRgIHdoaWNoIGlzIHJlbGF0ZWRcbiAgICAgIC8vIHRvIHRoZSBvbGQgUXVlcnlLZXkuIFdoaWNoIG1lYW5zIHRoYXQgY3VycmVudFJlc3VsdCBhbmQgc2VsZWN0RGF0YSBhcmVcbiAgICAgIC8vIG91dCBvZiBzeW5jIGFscmVhZHkuXG4gICAgICAvLyBUbyBzb2x2ZSB0aGlzLCB3ZSBtb3ZlIHRoZSBjdXJzb3Igb2YgdGhlIGN1cnJlbnRSZXN1bHQgZXZlcnl0aW1lXG4gICAgICAvLyBhbiBvYnNlcnZlciByZWFkcyBhbiBvcHRpbWlzdGljIHZhbHVlLlxuICAgICAgLy8gV2hlbiBrZWVwaW5nIHRoZSBwcmV2aW91cyBkYXRhLCB0aGUgcmVzdWx0IGRvZXNuJ3QgY2hhbmdlIHVudGlsIG5ld1xuICAgICAgLy8gZGF0YSBhcnJpdmVzLlxuICAgICAgdGhpcy5jdXJyZW50UmVzdWx0ID0gcmVzdWx0O1xuICAgICAgdGhpcy5jdXJyZW50UmVzdWx0T3B0aW9ucyA9IHRoaXMub3B0aW9ucztcbiAgICAgIHRoaXMuY3VycmVudFJlc3VsdFN0YXRlID0gdGhpcy5jdXJyZW50UXVlcnkuc3RhdGU7XG4gICAgfVxuXG4gICAgcmV0dXJuIHJlc3VsdDtcbiAgfVxuXG4gIGdldEN1cnJlbnRSZXN1bHQoKSB7XG4gICAgcmV0dXJuIHRoaXMuY3VycmVudFJlc3VsdDtcbiAgfVxuXG4gIHRyYWNrUmVzdWx0KHJlc3VsdCkge1xuICAgIGNvbnN0IHRyYWNrZWRSZXN1bHQgPSB7fTtcbiAgICBPYmplY3Qua2V5cyhyZXN1bHQpLmZvckVhY2goa2V5ID0+IHtcbiAgICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0cmFja2VkUmVzdWx0LCBrZXksIHtcbiAgICAgICAgY29uZmlndXJhYmxlOiBmYWxzZSxcbiAgICAgICAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgICAgICAgZ2V0OiAoKSA9PiB7XG4gICAgICAgICAgdGhpcy50cmFja2VkUHJvcHMuYWRkKGtleSk7XG4gICAgICAgICAgcmV0dXJuIHJlc3VsdFtrZXldO1xuICAgICAgICB9XG4gICAgICB9KTtcbiAgICB9KTtcbiAgICByZXR1cm4gdHJhY2tlZFJlc3VsdDtcbiAgfVxuXG4gIGdldEN1cnJlbnRRdWVyeSgpIHtcbiAgICByZXR1cm4gdGhpcy5jdXJyZW50UXVlcnk7XG4gIH1cblxuICByZW1vdmUoKSB7XG4gICAgdGhpcy5jbGllbnQuZ2V0UXVlcnlDYWNoZSgpLnJlbW92ZSh0aGlzLmN1cnJlbnRRdWVyeSk7XG4gIH1cblxuICByZWZldGNoKHtcbiAgICByZWZldGNoUGFnZSxcbiAgICAuLi5vcHRpb25zXG4gIH0gPSB7fSkge1xuICAgIHJldHVybiB0aGlzLmZldGNoKHsgLi4ub3B0aW9ucyxcbiAgICAgIG1ldGE6IHtcbiAgICAgICAgcmVmZXRjaFBhZ2VcbiAgICAgIH1cbiAgICB9KTtcbiAgfVxuXG4gIGZldGNoT3B0aW1pc3RpYyhvcHRpb25zKSB7XG4gICAgY29uc3QgZGVmYXVsdGVkT3B0aW9ucyA9IHRoaXMuY2xpZW50LmRlZmF1bHRRdWVyeU9wdGlvbnMob3B0aW9ucyk7XG4gICAgY29uc3QgcXVlcnkgPSB0aGlzLmNsaWVudC5nZXRRdWVyeUNhY2hlKCkuYnVpbGQodGhpcy5jbGllbnQsIGRlZmF1bHRlZE9wdGlvbnMpO1xuICAgIHF1ZXJ5LmlzRmV0Y2hpbmdPcHRpbWlzdGljID0gdHJ1ZTtcbiAgICByZXR1cm4gcXVlcnkuZmV0Y2goKS50aGVuKCgpID0+IHRoaXMuY3JlYXRlUmVzdWx0KHF1ZXJ5LCBkZWZhdWx0ZWRPcHRpb25zKSk7XG4gIH1cblxuICBmZXRjaChmZXRjaE9wdGlvbnMpIHtcbiAgICB2YXIgX2ZldGNoT3B0aW9ucyRjYW5jZWxSO1xuXG4gICAgcmV0dXJuIHRoaXMuZXhlY3V0ZUZldGNoKHsgLi4uZmV0Y2hPcHRpb25zLFxuICAgICAgY2FuY2VsUmVmZXRjaDogKF9mZXRjaE9wdGlvbnMkY2FuY2VsUiA9IGZldGNoT3B0aW9ucy5jYW5jZWxSZWZldGNoKSAhPSBudWxsID8gX2ZldGNoT3B0aW9ucyRjYW5jZWxSIDogdHJ1ZVxuICAgIH0pLnRoZW4oKCkgPT4ge1xuICAgICAgdGhpcy51cGRhdGVSZXN1bHQoKTtcbiAgICAgIHJldHVybiB0aGlzLmN1cnJlbnRSZXN1bHQ7XG4gICAgfSk7XG4gIH1cblxuICBleGVjdXRlRmV0Y2goZmV0Y2hPcHRpb25zKSB7XG4gICAgLy8gTWFrZSBzdXJlIHdlIHJlZmVyZW5jZSB0aGUgbGF0ZXN0IHF1ZXJ5IGFzIHRoZSBjdXJyZW50IG9uZSBtaWdodCBoYXZlIGJlZW4gcmVtb3ZlZFxuICAgIHRoaXMudXBkYXRlUXVlcnkoKTsgLy8gRmV0Y2hcblxuICAgIGxldCBwcm9taXNlID0gdGhpcy5jdXJyZW50UXVlcnkuZmV0Y2godGhpcy5vcHRpb25zLCBmZXRjaE9wdGlvbnMpO1xuXG4gICAgaWYgKCEoZmV0Y2hPcHRpb25zICE9IG51bGwgJiYgZmV0Y2hPcHRpb25zLnRocm93T25FcnJvcikpIHtcbiAgICAgIHByb21pc2UgPSBwcm9taXNlLmNhdGNoKG5vb3ApO1xuICAgIH1cblxuICAgIHJldHVybiBwcm9taXNlO1xuICB9XG5cbiAgdXBkYXRlU3RhbGVUaW1lb3V0KCkge1xuICAgIHRoaXMuY2xlYXJTdGFsZVRpbWVvdXQoKTtcblxuICAgIGlmIChpc1NlcnZlciB8fCB0aGlzLmN1cnJlbnRSZXN1bHQuaXNTdGFsZSB8fCAhaXNWYWxpZFRpbWVvdXQodGhpcy5vcHRpb25zLnN0YWxlVGltZSkpIHtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICBjb25zdCB0aW1lID0gdGltZVVudGlsU3RhbGUodGhpcy5jdXJyZW50UmVzdWx0LmRhdGFVcGRhdGVkQXQsIHRoaXMub3B0aW9ucy5zdGFsZVRpbWUpOyAvLyBUaGUgdGltZW91dCBpcyBzb21ldGltZXMgdHJpZ2dlcmVkIDEgbXMgYmVmb3JlIHRoZSBzdGFsZSB0aW1lIGV4cGlyYXRpb24uXG4gICAgLy8gVG8gbWl0aWdhdGUgdGhpcyBpc3N1ZSB3ZSBhbHdheXMgYWRkIDEgbXMgdG8gdGhlIHRpbWVvdXQuXG5cbiAgICBjb25zdCB0aW1lb3V0ID0gdGltZSArIDE7XG4gICAgdGhpcy5zdGFsZVRpbWVvdXRJZCA9IHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgaWYgKCF0aGlzLmN1cnJlbnRSZXN1bHQuaXNTdGFsZSkge1xuICAgICAgICB0aGlzLnVwZGF0ZVJlc3VsdCgpO1xuICAgICAgfVxuICAgIH0sIHRpbWVvdXQpO1xuICB9XG5cbiAgY29tcHV0ZVJlZmV0Y2hJbnRlcnZhbCgpIHtcbiAgICB2YXIgX3RoaXMkb3B0aW9ucyRyZWZldGNoO1xuXG4gICAgcmV0dXJuIHR5cGVvZiB0aGlzLm9wdGlvbnMucmVmZXRjaEludGVydmFsID09PSAnZnVuY3Rpb24nID8gdGhpcy5vcHRpb25zLnJlZmV0Y2hJbnRlcnZhbCh0aGlzLmN1cnJlbnRSZXN1bHQuZGF0YSwgdGhpcy5jdXJyZW50UXVlcnkpIDogKF90aGlzJG9wdGlvbnMkcmVmZXRjaCA9IHRoaXMub3B0aW9ucy5yZWZldGNoSW50ZXJ2YWwpICE9IG51bGwgPyBfdGhpcyRvcHRpb25zJHJlZmV0Y2ggOiBmYWxzZTtcbiAgfVxuXG4gIHVwZGF0ZVJlZmV0Y2hJbnRlcnZhbChuZXh0SW50ZXJ2YWwpIHtcbiAgICB0aGlzLmNsZWFyUmVmZXRjaEludGVydmFsKCk7XG4gICAgdGhpcy5jdXJyZW50UmVmZXRjaEludGVydmFsID0gbmV4dEludGVydmFsO1xuXG4gICAgaWYgKGlzU2VydmVyIHx8IHRoaXMub3B0aW9ucy5lbmFibGVkID09PSBmYWxzZSB8fCAhaXNWYWxpZFRpbWVvdXQodGhpcy5jdXJyZW50UmVmZXRjaEludGVydmFsKSB8fCB0aGlzLmN1cnJlbnRSZWZldGNoSW50ZXJ2YWwgPT09IDApIHtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICB0aGlzLnJlZmV0Y2hJbnRlcnZhbElkID0gc2V0SW50ZXJ2YWwoKCkgPT4ge1xuICAgICAgaWYgKHRoaXMub3B0aW9ucy5yZWZldGNoSW50ZXJ2YWxJbkJhY2tncm91bmQgfHwgZm9jdXNNYW5hZ2VyLmlzRm9jdXNlZCgpKSB7XG4gICAgICAgIHRoaXMuZXhlY3V0ZUZldGNoKCk7XG4gICAgICB9XG4gICAgfSwgdGhpcy5jdXJyZW50UmVmZXRjaEludGVydmFsKTtcbiAgfVxuXG4gIHVwZGF0ZVRpbWVycygpIHtcbiAgICB0aGlzLnVwZGF0ZVN0YWxlVGltZW91dCgpO1xuICAgIHRoaXMudXBkYXRlUmVmZXRjaEludGVydmFsKHRoaXMuY29tcHV0ZVJlZmV0Y2hJbnRlcnZhbCgpKTtcbiAgfVxuXG4gIGNsZWFyU3RhbGVUaW1lb3V0KCkge1xuICAgIGlmICh0aGlzLnN0YWxlVGltZW91dElkKSB7XG4gICAgICBjbGVhclRpbWVvdXQodGhpcy5zdGFsZVRpbWVvdXRJZCk7XG4gICAgICB0aGlzLnN0YWxlVGltZW91dElkID0gdW5kZWZpbmVkO1xuICAgIH1cbiAgfVxuXG4gIGNsZWFyUmVmZXRjaEludGVydmFsKCkge1xuICAgIGlmICh0aGlzLnJlZmV0Y2hJbnRlcnZhbElkKSB7XG4gICAgICBjbGVhckludGVydmFsKHRoaXMucmVmZXRjaEludGVydmFsSWQpO1xuICAgICAgdGhpcy5yZWZldGNoSW50ZXJ2YWxJZCA9IHVuZGVmaW5lZDtcbiAgICB9XG4gIH1cblxuICBjcmVhdGVSZXN1bHQocXVlcnksIG9wdGlvbnMpIHtcbiAgICBjb25zdCBwcmV2UXVlcnkgPSB0aGlzLmN1cnJlbnRRdWVyeTtcbiAgICBjb25zdCBwcmV2T3B0aW9ucyA9IHRoaXMub3B0aW9ucztcbiAgICBjb25zdCBwcmV2UmVzdWx0ID0gdGhpcy5jdXJyZW50UmVzdWx0O1xuICAgIGNvbnN0IHByZXZSZXN1bHRTdGF0ZSA9IHRoaXMuY3VycmVudFJlc3VsdFN0YXRlO1xuICAgIGNvbnN0IHByZXZSZXN1bHRPcHRpb25zID0gdGhpcy5jdXJyZW50UmVzdWx0T3B0aW9ucztcbiAgICBjb25zdCBxdWVyeUNoYW5nZSA9IHF1ZXJ5ICE9PSBwcmV2UXVlcnk7XG4gICAgY29uc3QgcXVlcnlJbml0aWFsU3RhdGUgPSBxdWVyeUNoYW5nZSA/IHF1ZXJ5LnN0YXRlIDogdGhpcy5jdXJyZW50UXVlcnlJbml0aWFsU3RhdGU7XG4gICAgY29uc3QgcHJldlF1ZXJ5UmVzdWx0ID0gcXVlcnlDaGFuZ2UgPyB0aGlzLmN1cnJlbnRSZXN1bHQgOiB0aGlzLnByZXZpb3VzUXVlcnlSZXN1bHQ7XG4gICAgY29uc3Qge1xuICAgICAgc3RhdGVcbiAgICB9ID0gcXVlcnk7XG4gICAgbGV0IHtcbiAgICAgIGRhdGFVcGRhdGVkQXQsXG4gICAgICBlcnJvcixcbiAgICAgIGVycm9yVXBkYXRlZEF0LFxuICAgICAgZmV0Y2hTdGF0dXMsXG4gICAgICBzdGF0dXNcbiAgICB9ID0gc3RhdGU7XG4gICAgbGV0IGlzUHJldmlvdXNEYXRhID0gZmFsc2U7XG4gICAgbGV0IGlzUGxhY2Vob2xkZXJEYXRhID0gZmFsc2U7XG4gICAgbGV0IGRhdGE7IC8vIE9wdGltaXN0aWNhbGx5IHNldCByZXN1bHQgaW4gZmV0Y2hpbmcgc3RhdGUgaWYgbmVlZGVkXG5cbiAgICBpZiAob3B0aW9ucy5fb3B0aW1pc3RpY1Jlc3VsdHMpIHtcbiAgICAgIGNvbnN0IG1vdW50ZWQgPSB0aGlzLmhhc0xpc3RlbmVycygpO1xuICAgICAgY29uc3QgZmV0Y2hPbk1vdW50ID0gIW1vdW50ZWQgJiYgc2hvdWxkRmV0Y2hPbk1vdW50KHF1ZXJ5LCBvcHRpb25zKTtcbiAgICAgIGNvbnN0IGZldGNoT3B0aW9uYWxseSA9IG1vdW50ZWQgJiYgc2hvdWxkRmV0Y2hPcHRpb25hbGx5KHF1ZXJ5LCBwcmV2UXVlcnksIG9wdGlvbnMsIHByZXZPcHRpb25zKTtcblxuICAgICAgaWYgKGZldGNoT25Nb3VudCB8fCBmZXRjaE9wdGlvbmFsbHkpIHtcbiAgICAgICAgZmV0Y2hTdGF0dXMgPSBjYW5GZXRjaChxdWVyeS5vcHRpb25zLm5ldHdvcmtNb2RlKSA/ICdmZXRjaGluZycgOiAncGF1c2VkJztcblxuICAgICAgICBpZiAoIWRhdGFVcGRhdGVkQXQpIHtcbiAgICAgICAgICBzdGF0dXMgPSAnbG9hZGluZyc7XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgaWYgKG9wdGlvbnMuX29wdGltaXN0aWNSZXN1bHRzID09PSAnaXNSZXN0b3JpbmcnKSB7XG4gICAgICAgIGZldGNoU3RhdHVzID0gJ2lkbGUnO1xuICAgICAgfVxuICAgIH0gLy8gS2VlcCBwcmV2aW91cyBkYXRhIGlmIG5lZWRlZFxuXG5cbiAgICBpZiAob3B0aW9ucy5rZWVwUHJldmlvdXNEYXRhICYmICFzdGF0ZS5kYXRhVXBkYXRlZEF0ICYmIHByZXZRdWVyeVJlc3VsdCAhPSBudWxsICYmIHByZXZRdWVyeVJlc3VsdC5pc1N1Y2Nlc3MgJiYgc3RhdHVzICE9PSAnZXJyb3InKSB7XG4gICAgICBkYXRhID0gcHJldlF1ZXJ5UmVzdWx0LmRhdGE7XG4gICAgICBkYXRhVXBkYXRlZEF0ID0gcHJldlF1ZXJ5UmVzdWx0LmRhdGFVcGRhdGVkQXQ7XG4gICAgICBzdGF0dXMgPSBwcmV2UXVlcnlSZXN1bHQuc3RhdHVzO1xuICAgICAgaXNQcmV2aW91c0RhdGEgPSB0cnVlO1xuICAgIH0gLy8gU2VsZWN0IGRhdGEgaWYgbmVlZGVkXG4gICAgZWxzZSBpZiAob3B0aW9ucy5zZWxlY3QgJiYgdHlwZW9mIHN0YXRlLmRhdGEgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAvLyBNZW1vaXplIHNlbGVjdCByZXN1bHRcbiAgICAgIGlmIChwcmV2UmVzdWx0ICYmIHN0YXRlLmRhdGEgPT09IChwcmV2UmVzdWx0U3RhdGUgPT0gbnVsbCA/IHZvaWQgMCA6IHByZXZSZXN1bHRTdGF0ZS5kYXRhKSAmJiBvcHRpb25zLnNlbGVjdCA9PT0gdGhpcy5zZWxlY3RGbikge1xuICAgICAgICBkYXRhID0gdGhpcy5zZWxlY3RSZXN1bHQ7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICB0cnkge1xuICAgICAgICAgIHRoaXMuc2VsZWN0Rm4gPSBvcHRpb25zLnNlbGVjdDtcbiAgICAgICAgICBkYXRhID0gb3B0aW9ucy5zZWxlY3Qoc3RhdGUuZGF0YSk7XG4gICAgICAgICAgZGF0YSA9IHJlcGxhY2VEYXRhKHByZXZSZXN1bHQgPT0gbnVsbCA/IHZvaWQgMCA6IHByZXZSZXN1bHQuZGF0YSwgZGF0YSwgb3B0aW9ucyk7XG4gICAgICAgICAgdGhpcy5zZWxlY3RSZXN1bHQgPSBkYXRhO1xuICAgICAgICAgIHRoaXMuc2VsZWN0RXJyb3IgPSBudWxsO1xuICAgICAgICB9IGNhdGNoIChzZWxlY3RFcnJvcikge1xuICAgICAgICAgIGlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSB7XG4gICAgICAgICAgICB0aGlzLmNsaWVudC5nZXRMb2dnZXIoKS5lcnJvcihzZWxlY3RFcnJvcik7XG4gICAgICAgICAgfVxuXG4gICAgICAgICAgdGhpcy5zZWxlY3RFcnJvciA9IHNlbGVjdEVycm9yO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfSAvLyBVc2UgcXVlcnkgZGF0YVxuICAgIGVsc2Uge1xuICAgICAgZGF0YSA9IHN0YXRlLmRhdGE7XG4gICAgfSAvLyBTaG93IHBsYWNlaG9sZGVyIGRhdGEgaWYgbmVlZGVkXG5cblxuICAgIGlmICh0eXBlb2Ygb3B0aW9ucy5wbGFjZWhvbGRlckRhdGEgIT09ICd1bmRlZmluZWQnICYmIHR5cGVvZiBkYXRhID09PSAndW5kZWZpbmVkJyAmJiBzdGF0dXMgPT09ICdsb2FkaW5nJykge1xuICAgICAgbGV0IHBsYWNlaG9sZGVyRGF0YTsgLy8gTWVtb2l6ZSBwbGFjZWhvbGRlciBkYXRhXG5cbiAgICAgIGlmIChwcmV2UmVzdWx0ICE9IG51bGwgJiYgcHJldlJlc3VsdC5pc1BsYWNlaG9sZGVyRGF0YSAmJiBvcHRpb25zLnBsYWNlaG9sZGVyRGF0YSA9PT0gKHByZXZSZXN1bHRPcHRpb25zID09IG51bGwgPyB2b2lkIDAgOiBwcmV2UmVzdWx0T3B0aW9ucy5wbGFjZWhvbGRlckRhdGEpKSB7XG4gICAgICAgIHBsYWNlaG9sZGVyRGF0YSA9IHByZXZSZXN1bHQuZGF0YTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHBsYWNlaG9sZGVyRGF0YSA9IHR5cGVvZiBvcHRpb25zLnBsYWNlaG9sZGVyRGF0YSA9PT0gJ2Z1bmN0aW9uJyA/IG9wdGlvbnMucGxhY2Vob2xkZXJEYXRhKCkgOiBvcHRpb25zLnBsYWNlaG9sZGVyRGF0YTtcblxuICAgICAgICBpZiAob3B0aW9ucy5zZWxlY3QgJiYgdHlwZW9mIHBsYWNlaG9sZGVyRGF0YSAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgcGxhY2Vob2xkZXJEYXRhID0gb3B0aW9ucy5zZWxlY3QocGxhY2Vob2xkZXJEYXRhKTtcbiAgICAgICAgICAgIHRoaXMuc2VsZWN0RXJyb3IgPSBudWxsO1xuICAgICAgICAgIH0gY2F0Y2ggKHNlbGVjdEVycm9yKSB7XG4gICAgICAgICAgICBpZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykge1xuICAgICAgICAgICAgICB0aGlzLmNsaWVudC5nZXRMb2dnZXIoKS5lcnJvcihzZWxlY3RFcnJvcik7XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgIHRoaXMuc2VsZWN0RXJyb3IgPSBzZWxlY3RFcnJvcjtcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgaWYgKHR5cGVvZiBwbGFjZWhvbGRlckRhdGEgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgIHN0YXR1cyA9ICdzdWNjZXNzJztcbiAgICAgICAgZGF0YSA9IHJlcGxhY2VEYXRhKHByZXZSZXN1bHQgPT0gbnVsbCA/IHZvaWQgMCA6IHByZXZSZXN1bHQuZGF0YSwgcGxhY2Vob2xkZXJEYXRhLCBvcHRpb25zKTtcbiAgICAgICAgaXNQbGFjZWhvbGRlckRhdGEgPSB0cnVlO1xuICAgICAgfVxuICAgIH1cblxuICAgIGlmICh0aGlzLnNlbGVjdEVycm9yKSB7XG4gICAgICBlcnJvciA9IHRoaXMuc2VsZWN0RXJyb3I7XG4gICAgICBkYXRhID0gdGhpcy5zZWxlY3RSZXN1bHQ7XG4gICAgICBlcnJvclVwZGF0ZWRBdCA9IERhdGUubm93KCk7XG4gICAgICBzdGF0dXMgPSAnZXJyb3InO1xuICAgIH1cblxuICAgIGNvbnN0IGlzRmV0Y2hpbmcgPSBmZXRjaFN0YXR1cyA9PT0gJ2ZldGNoaW5nJztcbiAgICBjb25zdCBpc0xvYWRpbmcgPSBzdGF0dXMgPT09ICdsb2FkaW5nJztcbiAgICBjb25zdCBpc0Vycm9yID0gc3RhdHVzID09PSAnZXJyb3InO1xuICAgIGNvbnN0IHJlc3VsdCA9IHtcbiAgICAgIHN0YXR1cyxcbiAgICAgIGZldGNoU3RhdHVzLFxuICAgICAgaXNMb2FkaW5nLFxuICAgICAgaXNTdWNjZXNzOiBzdGF0dXMgPT09ICdzdWNjZXNzJyxcbiAgICAgIGlzRXJyb3IsXG4gICAgICBpc0luaXRpYWxMb2FkaW5nOiBpc0xvYWRpbmcgJiYgaXNGZXRjaGluZyxcbiAgICAgIGRhdGEsXG4gICAgICBkYXRhVXBkYXRlZEF0LFxuICAgICAgZXJyb3IsXG4gICAgICBlcnJvclVwZGF0ZWRBdCxcbiAgICAgIGZhaWx1cmVDb3VudDogc3RhdGUuZmV0Y2hGYWlsdXJlQ291bnQsXG4gICAgICBmYWlsdXJlUmVhc29uOiBzdGF0ZS5mZXRjaEZhaWx1cmVSZWFzb24sXG4gICAgICBlcnJvclVwZGF0ZUNvdW50OiBzdGF0ZS5lcnJvclVwZGF0ZUNvdW50LFxuICAgICAgaXNGZXRjaGVkOiBzdGF0ZS5kYXRhVXBkYXRlQ291bnQgPiAwIHx8IHN0YXRlLmVycm9yVXBkYXRlQ291bnQgPiAwLFxuICAgICAgaXNGZXRjaGVkQWZ0ZXJNb3VudDogc3RhdGUuZGF0YVVwZGF0ZUNvdW50ID4gcXVlcnlJbml0aWFsU3RhdGUuZGF0YVVwZGF0ZUNvdW50IHx8IHN0YXRlLmVycm9yVXBkYXRlQ291bnQgPiBxdWVyeUluaXRpYWxTdGF0ZS5lcnJvclVwZGF0ZUNvdW50LFxuICAgICAgaXNGZXRjaGluZyxcbiAgICAgIGlzUmVmZXRjaGluZzogaXNGZXRjaGluZyAmJiAhaXNMb2FkaW5nLFxuICAgICAgaXNMb2FkaW5nRXJyb3I6IGlzRXJyb3IgJiYgc3RhdGUuZGF0YVVwZGF0ZWRBdCA9PT0gMCxcbiAgICAgIGlzUGF1c2VkOiBmZXRjaFN0YXR1cyA9PT0gJ3BhdXNlZCcsXG4gICAgICBpc1BsYWNlaG9sZGVyRGF0YSxcbiAgICAgIGlzUHJldmlvdXNEYXRhLFxuICAgICAgaXNSZWZldGNoRXJyb3I6IGlzRXJyb3IgJiYgc3RhdGUuZGF0YVVwZGF0ZWRBdCAhPT0gMCxcbiAgICAgIGlzU3RhbGU6IGlzU3RhbGUocXVlcnksIG9wdGlvbnMpLFxuICAgICAgcmVmZXRjaDogdGhpcy5yZWZldGNoLFxuICAgICAgcmVtb3ZlOiB0aGlzLnJlbW92ZVxuICAgIH07XG4gICAgcmV0dXJuIHJlc3VsdDtcbiAgfVxuXG4gIHVwZGF0ZVJlc3VsdChub3RpZnlPcHRpb25zKSB7XG4gICAgY29uc3QgcHJldlJlc3VsdCA9IHRoaXMuY3VycmVudFJlc3VsdDtcbiAgICBjb25zdCBuZXh0UmVzdWx0ID0gdGhpcy5jcmVhdGVSZXN1bHQodGhpcy5jdXJyZW50UXVlcnksIHRoaXMub3B0aW9ucyk7XG4gICAgdGhpcy5jdXJyZW50UmVzdWx0U3RhdGUgPSB0aGlzLmN1cnJlbnRRdWVyeS5zdGF0ZTtcbiAgICB0aGlzLmN1cnJlbnRSZXN1bHRPcHRpb25zID0gdGhpcy5vcHRpb25zOyAvLyBPbmx5IG5vdGlmeSBhbmQgdXBkYXRlIHJlc3VsdCBpZiBzb21ldGhpbmcgaGFzIGNoYW5nZWRcblxuICAgIGlmIChzaGFsbG93RXF1YWxPYmplY3RzKG5leHRSZXN1bHQsIHByZXZSZXN1bHQpKSB7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgdGhpcy5jdXJyZW50UmVzdWx0ID0gbmV4dFJlc3VsdDsgLy8gRGV0ZXJtaW5lIHdoaWNoIGNhbGxiYWNrcyB0byB0cmlnZ2VyXG5cbiAgICBjb25zdCBkZWZhdWx0Tm90aWZ5T3B0aW9ucyA9IHtcbiAgICAgIGNhY2hlOiB0cnVlXG4gICAgfTtcblxuICAgIGNvbnN0IHNob3VsZE5vdGlmeUxpc3RlbmVycyA9ICgpID0+IHtcbiAgICAgIGlmICghcHJldlJlc3VsdCkge1xuICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICAgIH1cblxuICAgICAgY29uc3Qge1xuICAgICAgICBub3RpZnlPbkNoYW5nZVByb3BzXG4gICAgICB9ID0gdGhpcy5vcHRpb25zO1xuICAgICAgY29uc3Qgbm90aWZ5T25DaGFuZ2VQcm9wc1ZhbHVlID0gdHlwZW9mIG5vdGlmeU9uQ2hhbmdlUHJvcHMgPT09ICdmdW5jdGlvbicgPyBub3RpZnlPbkNoYW5nZVByb3BzKCkgOiBub3RpZnlPbkNoYW5nZVByb3BzO1xuXG4gICAgICBpZiAobm90aWZ5T25DaGFuZ2VQcm9wc1ZhbHVlID09PSAnYWxsJyB8fCAhbm90aWZ5T25DaGFuZ2VQcm9wc1ZhbHVlICYmICF0aGlzLnRyYWNrZWRQcm9wcy5zaXplKSB7XG4gICAgICAgIHJldHVybiB0cnVlO1xuICAgICAgfVxuXG4gICAgICBjb25zdCBpbmNsdWRlZFByb3BzID0gbmV3IFNldChub3RpZnlPbkNoYW5nZVByb3BzVmFsdWUgIT0gbnVsbCA/IG5vdGlmeU9uQ2hhbmdlUHJvcHNWYWx1ZSA6IHRoaXMudHJhY2tlZFByb3BzKTtcblxuICAgICAgaWYgKHRoaXMub3B0aW9ucy51c2VFcnJvckJvdW5kYXJ5KSB7XG4gICAgICAgIGluY2x1ZGVkUHJvcHMuYWRkKCdlcnJvcicpO1xuICAgICAgfVxuXG4gICAgICByZXR1cm4gT2JqZWN0LmtleXModGhpcy5jdXJyZW50UmVzdWx0KS5zb21lKGtleSA9PiB7XG4gICAgICAgIGNvbnN0IHR5cGVkS2V5ID0ga2V5O1xuICAgICAgICBjb25zdCBjaGFuZ2VkID0gdGhpcy5jdXJyZW50UmVzdWx0W3R5cGVkS2V5XSAhPT0gcHJldlJlc3VsdFt0eXBlZEtleV07XG4gICAgICAgIHJldHVybiBjaGFuZ2VkICYmIGluY2x1ZGVkUHJvcHMuaGFzKHR5cGVkS2V5KTtcbiAgICAgIH0pO1xuICAgIH07XG5cbiAgICBpZiAoKG5vdGlmeU9wdGlvbnMgPT0gbnVsbCA/IHZvaWQgMCA6IG5vdGlmeU9wdGlvbnMubGlzdGVuZXJzKSAhPT0gZmFsc2UgJiYgc2hvdWxkTm90aWZ5TGlzdGVuZXJzKCkpIHtcbiAgICAgIGRlZmF1bHROb3RpZnlPcHRpb25zLmxpc3RlbmVycyA9IHRydWU7XG4gICAgfVxuXG4gICAgdGhpcy5ub3RpZnkoeyAuLi5kZWZhdWx0Tm90aWZ5T3B0aW9ucyxcbiAgICAgIC4uLm5vdGlmeU9wdGlvbnNcbiAgICB9KTtcbiAgfVxuXG4gIHVwZGF0ZVF1ZXJ5KCkge1xuICAgIGNvbnN0IHF1ZXJ5ID0gdGhpcy5jbGllbnQuZ2V0UXVlcnlDYWNoZSgpLmJ1aWxkKHRoaXMuY2xpZW50LCB0aGlzLm9wdGlvbnMpO1xuXG4gICAgaWYgKHF1ZXJ5ID09PSB0aGlzLmN1cnJlbnRRdWVyeSkge1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIGNvbnN0IHByZXZRdWVyeSA9IHRoaXMuY3VycmVudFF1ZXJ5O1xuICAgIHRoaXMuY3VycmVudFF1ZXJ5ID0gcXVlcnk7XG4gICAgdGhpcy5jdXJyZW50UXVlcnlJbml0aWFsU3RhdGUgPSBxdWVyeS5zdGF0ZTtcbiAgICB0aGlzLnByZXZpb3VzUXVlcnlSZXN1bHQgPSB0aGlzLmN1cnJlbnRSZXN1bHQ7XG5cbiAgICBpZiAodGhpcy5oYXNMaXN0ZW5lcnMoKSkge1xuICAgICAgcHJldlF1ZXJ5ID09IG51bGwgPyB2b2lkIDAgOiBwcmV2UXVlcnkucmVtb3ZlT2JzZXJ2ZXIodGhpcyk7XG4gICAgICBxdWVyeS5hZGRPYnNlcnZlcih0aGlzKTtcbiAgICB9XG4gIH1cblxuICBvblF1ZXJ5VXBkYXRlKGFjdGlvbikge1xuICAgIGNvbnN0IG5vdGlmeU9wdGlvbnMgPSB7fTtcblxuICAgIGlmIChhY3Rpb24udHlwZSA9PT0gJ3N1Y2Nlc3MnKSB7XG4gICAgICBub3RpZnlPcHRpb25zLm9uU3VjY2VzcyA9ICFhY3Rpb24ubWFudWFsO1xuICAgIH0gZWxzZSBpZiAoYWN0aW9uLnR5cGUgPT09ICdlcnJvcicgJiYgIWlzQ2FuY2VsbGVkRXJyb3IoYWN0aW9uLmVycm9yKSkge1xuICAgICAgbm90aWZ5T3B0aW9ucy5vbkVycm9yID0gdHJ1ZTtcbiAgICB9XG5cbiAgICB0aGlzLnVwZGF0ZVJlc3VsdChub3RpZnlPcHRpb25zKTtcblxuICAgIGlmICh0aGlzLmhhc0xpc3RlbmVycygpKSB7XG4gICAgICB0aGlzLnVwZGF0ZVRpbWVycygpO1xuICAgIH1cbiAgfVxuXG4gIG5vdGlmeShub3RpZnlPcHRpb25zKSB7XG4gICAgbm90aWZ5TWFuYWdlci5iYXRjaCgoKSA9PiB7XG4gICAgICAvLyBGaXJzdCB0cmlnZ2VyIHRoZSBjb25maWd1cmF0aW9uIGNhbGxiYWNrc1xuICAgICAgaWYgKG5vdGlmeU9wdGlvbnMub25TdWNjZXNzKSB7XG4gICAgICAgIHZhciBfdGhpcyRvcHRpb25zJG9uU3VjY2UsIF90aGlzJG9wdGlvbnMsIF90aGlzJG9wdGlvbnMkb25TZXR0bCwgX3RoaXMkb3B0aW9uczI7XG5cbiAgICAgICAgKF90aGlzJG9wdGlvbnMkb25TdWNjZSA9IChfdGhpcyRvcHRpb25zID0gdGhpcy5vcHRpb25zKS5vblN1Y2Nlc3MpID09IG51bGwgPyB2b2lkIDAgOiBfdGhpcyRvcHRpb25zJG9uU3VjY2UuY2FsbChfdGhpcyRvcHRpb25zLCB0aGlzLmN1cnJlbnRSZXN1bHQuZGF0YSk7XG4gICAgICAgIChfdGhpcyRvcHRpb25zJG9uU2V0dGwgPSAoX3RoaXMkb3B0aW9uczIgPSB0aGlzLm9wdGlvbnMpLm9uU2V0dGxlZCkgPT0gbnVsbCA/IHZvaWQgMCA6IF90aGlzJG9wdGlvbnMkb25TZXR0bC5jYWxsKF90aGlzJG9wdGlvbnMyLCB0aGlzLmN1cnJlbnRSZXN1bHQuZGF0YSwgbnVsbCk7XG4gICAgICB9IGVsc2UgaWYgKG5vdGlmeU9wdGlvbnMub25FcnJvcikge1xuICAgICAgICB2YXIgX3RoaXMkb3B0aW9ucyRvbkVycm9yLCBfdGhpcyRvcHRpb25zMywgX3RoaXMkb3B0aW9ucyRvblNldHRsMiwgX3RoaXMkb3B0aW9uczQ7XG5cbiAgICAgICAgKF90aGlzJG9wdGlvbnMkb25FcnJvciA9IChfdGhpcyRvcHRpb25zMyA9IHRoaXMub3B0aW9ucykub25FcnJvcikgPT0gbnVsbCA/IHZvaWQgMCA6IF90aGlzJG9wdGlvbnMkb25FcnJvci5jYWxsKF90aGlzJG9wdGlvbnMzLCB0aGlzLmN1cnJlbnRSZXN1bHQuZXJyb3IpO1xuICAgICAgICAoX3RoaXMkb3B0aW9ucyRvblNldHRsMiA9IChfdGhpcyRvcHRpb25zNCA9IHRoaXMub3B0aW9ucykub25TZXR0bGVkKSA9PSBudWxsID8gdm9pZCAwIDogX3RoaXMkb3B0aW9ucyRvblNldHRsMi5jYWxsKF90aGlzJG9wdGlvbnM0LCB1bmRlZmluZWQsIHRoaXMuY3VycmVudFJlc3VsdC5lcnJvcik7XG4gICAgICB9IC8vIFRoZW4gdHJpZ2dlciB0aGUgbGlzdGVuZXJzXG5cblxuICAgICAgaWYgKG5vdGlmeU9wdGlvbnMubGlzdGVuZXJzKSB7XG4gICAgICAgIHRoaXMubGlzdGVuZXJzLmZvckVhY2goKHtcbiAgICAgICAgICBsaXN0ZW5lclxuICAgICAgICB9KSA9PiB7XG4gICAgICAgICAgbGlzdGVuZXIodGhpcy5jdXJyZW50UmVzdWx0KTtcbiAgICAgICAgfSk7XG4gICAgICB9IC8vIFRoZW4gdGhlIGNhY2hlIGxpc3RlbmVyc1xuXG5cbiAgICAgIGlmIChub3RpZnlPcHRpb25zLmNhY2hlKSB7XG4gICAgICAgIHRoaXMuY2xpZW50LmdldFF1ZXJ5Q2FjaGUoKS5ub3RpZnkoe1xuICAgICAgICAgIHF1ZXJ5OiB0aGlzLmN1cnJlbnRRdWVyeSxcbiAgICAgICAgICB0eXBlOiAnb2JzZXJ2ZXJSZXN1bHRzVXBkYXRlZCdcbiAgICAgICAgfSk7XG4gICAgICB9XG4gICAgfSk7XG4gIH1cblxufVxuXG5mdW5jdGlvbiBzaG91bGRMb2FkT25Nb3VudChxdWVyeSwgb3B0aW9ucykge1xuICByZXR1cm4gb3B0aW9ucy5lbmFibGVkICE9PSBmYWxzZSAmJiAhcXVlcnkuc3RhdGUuZGF0YVVwZGF0ZWRBdCAmJiAhKHF1ZXJ5LnN0YXRlLnN0YXR1cyA9PT0gJ2Vycm9yJyAmJiBvcHRpb25zLnJldHJ5T25Nb3VudCA9PT0gZmFsc2UpO1xufVxuXG5mdW5jdGlvbiBzaG91bGRGZXRjaE9uTW91bnQocXVlcnksIG9wdGlvbnMpIHtcbiAgcmV0dXJuIHNob3VsZExvYWRPbk1vdW50KHF1ZXJ5LCBvcHRpb25zKSB8fCBxdWVyeS5zdGF0ZS5kYXRhVXBkYXRlZEF0ID4gMCAmJiBzaG91bGRGZXRjaE9uKHF1ZXJ5LCBvcHRpb25zLCBvcHRpb25zLnJlZmV0Y2hPbk1vdW50KTtcbn1cblxuZnVuY3Rpb24gc2hvdWxkRmV0Y2hPbihxdWVyeSwgb3B0aW9ucywgZmllbGQpIHtcbiAgaWYgKG9wdGlvbnMuZW5hYmxlZCAhPT0gZmFsc2UpIHtcbiAgICBjb25zdCB2YWx1ZSA9IHR5cGVvZiBmaWVsZCA9PT0gJ2Z1bmN0aW9uJyA/IGZpZWxkKHF1ZXJ5KSA6IGZpZWxkO1xuICAgIHJldHVybiB2YWx1ZSA9PT0gJ2Fsd2F5cycgfHwgdmFsdWUgIT09IGZhbHNlICYmIGlzU3RhbGUocXVlcnksIG9wdGlvbnMpO1xuICB9XG5cbiAgcmV0dXJuIGZhbHNlO1xufVxuXG5mdW5jdGlvbiBzaG91bGRGZXRjaE9wdGlvbmFsbHkocXVlcnksIHByZXZRdWVyeSwgb3B0aW9ucywgcHJldk9wdGlvbnMpIHtcbiAgcmV0dXJuIG9wdGlvbnMuZW5hYmxlZCAhPT0gZmFsc2UgJiYgKHF1ZXJ5ICE9PSBwcmV2UXVlcnkgfHwgcHJldk9wdGlvbnMuZW5hYmxlZCA9PT0gZmFsc2UpICYmICghb3B0aW9ucy5zdXNwZW5zZSB8fCBxdWVyeS5zdGF0ZS5zdGF0dXMgIT09ICdlcnJvcicpICYmIGlzU3RhbGUocXVlcnksIG9wdGlvbnMpO1xufVxuXG5mdW5jdGlvbiBpc1N0YWxlKHF1ZXJ5LCBvcHRpb25zKSB7XG4gIHJldHVybiBxdWVyeS5pc1N0YWxlQnlUaW1lKG9wdGlvbnMuc3RhbGVUaW1lKTtcbn0gLy8gdGhpcyBmdW5jdGlvbiB3b3VsZCBkZWNpZGUgaWYgd2Ugd2lsbCB1cGRhdGUgdGhlIG9ic2VydmVyJ3MgJ2N1cnJlbnQnXG4vLyBwcm9wZXJ0aWVzIGFmdGVyIGFuIG9wdGltaXN0aWMgcmVhZGluZyB2aWEgZ2V0T3B0aW1pc3RpY1Jlc3VsdFxuXG5cbmZ1bmN0aW9uIHNob3VsZEFzc2lnbk9ic2VydmVyQ3VycmVudFByb3BlcnRpZXMob2JzZXJ2ZXIsIG9wdGltaXN0aWNSZXN1bHQsIG9wdGlvbnMpIHtcbiAgLy8gaXQgaXMgaW1wb3J0YW50IHRvIGtlZXAgdGhpcyBjb25kaXRpb24gbGlrZSB0aGlzIGZvciB0aHJlZSByZWFzb25zOlxuICAvLyAxLiBJdCB3aWxsIGdldCByZW1vdmVkIGluIHRoZSB2NVxuICAvLyAyLiBpdCByZWFkczogZG9uJ3QgdXBkYXRlIHRoZSBwcm9wZXJ0aWVzIGlmIHdlIHdhbnQgdG8ga2VlcCB0aGUgcHJldmlvdXNcbiAgLy8gZGF0YS5cbiAgLy8gMy4gVGhlIG9wcG9zaXRlIGNvbmRpdGlvbiAoIW9wdGlvbnMua2VlcFByZXZpb3VzRGF0YSkgd291bGQgZmFsbHRocm91Z2hcbiAgLy8gYW5kIHdpbGwgcmVzdWx0IGluIGEgYmFkIGRlY2lzaW9uXG4gIGlmIChvcHRpb25zLmtlZXBQcmV2aW91c0RhdGEpIHtcbiAgICByZXR1cm4gZmFsc2U7XG4gIH0gLy8gdGhpcyBtZWFucyB3ZSB3YW50IHRvIHB1dCBzb21lIHBsYWNlaG9sZGVyIGRhdGEgd2hlbiBwZW5kaW5nIGFuZCBxdWVyeUtleVxuICAvLyBjaGFuZ2VkLlxuXG5cbiAgaWYgKG9wdGlvbnMucGxhY2Vob2xkZXJEYXRhICE9PSB1bmRlZmluZWQpIHtcbiAgICAvLyByZS1hc3NpZ24gcHJvcGVydGllcyBvbmx5IGlmIGN1cnJlbnQgZGF0YSBpcyBwbGFjZWhvbGRlciBkYXRhXG4gICAgLy8gd2hpY2ggbWVhbnMgdGhhdCBkYXRhIGRpZCBub3QgYXJyaXZlIHlldCwgc28sIGlmIHRoZXJlIGlzIHNvbWUgY2FjaGVkIGRhdGFcbiAgICAvLyB3ZSBuZWVkIHRvIFwicHJlcGFyZVwiIHRvIHJlY2VpdmUgaXRcbiAgICByZXR1cm4gb3B0aW1pc3RpY1Jlc3VsdC5pc1BsYWNlaG9sZGVyRGF0YTtcbiAgfSAvLyBpZiB0aGUgbmV3bHkgY3JlYXRlZCByZXN1bHQgaXNuJ3Qgd2hhdCB0aGUgb2JzZXJ2ZXIgaXMgaG9sZGluZyBhcyBjdXJyZW50LFxuICAvLyB0aGVuIHdlJ2xsIG5lZWQgdG8gdXBkYXRlIHRoZSBwcm9wZXJ0aWVzIGFzIHdlbGxcblxuXG4gIGlmICghc2hhbGxvd0VxdWFsT2JqZWN0cyhvYnNlcnZlci5nZXRDdXJyZW50UmVzdWx0KCksIG9wdGltaXN0aWNSZXN1bHQpKSB7XG4gICAgcmV0dXJuIHRydWU7XG4gIH0gLy8gYmFzaWNhbGx5LCBqdXN0IGtlZXAgcHJldmlvdXMgcHJvcGVydGllcyBpZiBub3RoaW5nIGNoYW5nZWRcblxuXG4gIHJldHVybiBmYWxzZTtcbn1cblxuZXhwb3J0IHsgUXVlcnlPYnNlcnZlciB9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cXVlcnlPYnNlcnZlci5tanMubWFwXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/lib/queryObserver.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/lib/removable.mjs":
/*!*******************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/lib/removable.mjs ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Removable: () => (/* binding */ Removable)\n/* harmony export */ });\n/* harmony import */ var _utils_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/utils.mjs\");\n\n\nclass Removable {\n  destroy() {\n    this.clearGcTimeout();\n  }\n\n  scheduleGc() {\n    this.clearGcTimeout();\n\n    if ((0,_utils_mjs__WEBPACK_IMPORTED_MODULE_0__.isValidTimeout)(this.cacheTime)) {\n      this.gcTimeout = setTimeout(() => {\n        this.optionalRemove();\n      }, this.cacheTime);\n    }\n  }\n\n  updateCacheTime(newCacheTime) {\n    // Default to 5 minutes (Infinity for server-side) if no cache time is set\n    this.cacheTime = Math.max(this.cacheTime || 0, newCacheTime != null ? newCacheTime : _utils_mjs__WEBPACK_IMPORTED_MODULE_0__.isServer ? Infinity : 5 * 60 * 1000);\n  }\n\n  clearGcTimeout() {\n    if (this.gcTimeout) {\n      clearTimeout(this.gcTimeout);\n      this.gcTimeout = undefined;\n    }\n  }\n\n}\n\n\n//# sourceMappingURL=removable.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRhbnN0YWNrL3F1ZXJ5LWNvcmUvYnVpbGQvbGliL3JlbW92YWJsZS5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBdUQ7O0FBRXZEO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUEsUUFBUSwwREFBYztBQUN0QjtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLHlGQUF5RixnREFBUTtBQUNqRzs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRXFCO0FBQ3JCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJlcmthXFxEZXNrdG9wXFxSRU5FUk9cXG5leHRfcmVuZXJvXFxub2RlX21vZHVsZXNcXEB0YW5zdGFja1xccXVlcnktY29yZVxcYnVpbGRcXGxpYlxccmVtb3ZhYmxlLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBpc1ZhbGlkVGltZW91dCwgaXNTZXJ2ZXIgfSBmcm9tICcuL3V0aWxzLm1qcyc7XG5cbmNsYXNzIFJlbW92YWJsZSB7XG4gIGRlc3Ryb3koKSB7XG4gICAgdGhpcy5jbGVhckdjVGltZW91dCgpO1xuICB9XG5cbiAgc2NoZWR1bGVHYygpIHtcbiAgICB0aGlzLmNsZWFyR2NUaW1lb3V0KCk7XG5cbiAgICBpZiAoaXNWYWxpZFRpbWVvdXQodGhpcy5jYWNoZVRpbWUpKSB7XG4gICAgICB0aGlzLmdjVGltZW91dCA9IHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICB0aGlzLm9wdGlvbmFsUmVtb3ZlKCk7XG4gICAgICB9LCB0aGlzLmNhY2hlVGltZSk7XG4gICAgfVxuICB9XG5cbiAgdXBkYXRlQ2FjaGVUaW1lKG5ld0NhY2hlVGltZSkge1xuICAgIC8vIERlZmF1bHQgdG8gNSBtaW51dGVzIChJbmZpbml0eSBmb3Igc2VydmVyLXNpZGUpIGlmIG5vIGNhY2hlIHRpbWUgaXMgc2V0XG4gICAgdGhpcy5jYWNoZVRpbWUgPSBNYXRoLm1heCh0aGlzLmNhY2hlVGltZSB8fCAwLCBuZXdDYWNoZVRpbWUgIT0gbnVsbCA/IG5ld0NhY2hlVGltZSA6IGlzU2VydmVyID8gSW5maW5pdHkgOiA1ICogNjAgKiAxMDAwKTtcbiAgfVxuXG4gIGNsZWFyR2NUaW1lb3V0KCkge1xuICAgIGlmICh0aGlzLmdjVGltZW91dCkge1xuICAgICAgY2xlYXJUaW1lb3V0KHRoaXMuZ2NUaW1lb3V0KTtcbiAgICAgIHRoaXMuZ2NUaW1lb3V0ID0gdW5kZWZpbmVkO1xuICAgIH1cbiAgfVxuXG59XG5cbmV4cG9ydCB7IFJlbW92YWJsZSB9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cmVtb3ZhYmxlLm1qcy5tYXBcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/lib/removable.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/lib/retryer.mjs":
/*!*****************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/lib/retryer.mjs ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CancelledError: () => (/* binding */ CancelledError),\n/* harmony export */   canFetch: () => (/* binding */ canFetch),\n/* harmony export */   createRetryer: () => (/* binding */ createRetryer),\n/* harmony export */   isCancelledError: () => (/* binding */ isCancelledError)\n/* harmony export */ });\n/* harmony import */ var _focusManager_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./focusManager.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/focusManager.mjs\");\n/* harmony import */ var _onlineManager_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./onlineManager.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/onlineManager.mjs\");\n/* harmony import */ var _utils_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils.mjs */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/utils.mjs\");\n\n\n\n\nfunction defaultRetryDelay(failureCount) {\n  return Math.min(1000 * 2 ** failureCount, 30000);\n}\n\nfunction canFetch(networkMode) {\n  return (networkMode != null ? networkMode : 'online') === 'online' ? _onlineManager_mjs__WEBPACK_IMPORTED_MODULE_0__.onlineManager.isOnline() : true;\n}\nclass CancelledError {\n  constructor(options) {\n    this.revert = options == null ? void 0 : options.revert;\n    this.silent = options == null ? void 0 : options.silent;\n  }\n\n}\nfunction isCancelledError(value) {\n  return value instanceof CancelledError;\n}\nfunction createRetryer(config) {\n  let isRetryCancelled = false;\n  let failureCount = 0;\n  let isResolved = false;\n  let continueFn;\n  let promiseResolve;\n  let promiseReject;\n  const promise = new Promise((outerResolve, outerReject) => {\n    promiseResolve = outerResolve;\n    promiseReject = outerReject;\n  });\n\n  const cancel = cancelOptions => {\n    if (!isResolved) {\n      reject(new CancelledError(cancelOptions));\n      config.abort == null ? void 0 : config.abort();\n    }\n  };\n\n  const cancelRetry = () => {\n    isRetryCancelled = true;\n  };\n\n  const continueRetry = () => {\n    isRetryCancelled = false;\n  };\n\n  const shouldPause = () => !_focusManager_mjs__WEBPACK_IMPORTED_MODULE_1__.focusManager.isFocused() || config.networkMode !== 'always' && !_onlineManager_mjs__WEBPACK_IMPORTED_MODULE_0__.onlineManager.isOnline();\n\n  const resolve = value => {\n    if (!isResolved) {\n      isResolved = true;\n      config.onSuccess == null ? void 0 : config.onSuccess(value);\n      continueFn == null ? void 0 : continueFn();\n      promiseResolve(value);\n    }\n  };\n\n  const reject = value => {\n    if (!isResolved) {\n      isResolved = true;\n      config.onError == null ? void 0 : config.onError(value);\n      continueFn == null ? void 0 : continueFn();\n      promiseReject(value);\n    }\n  };\n\n  const pause = () => {\n    return new Promise(continueResolve => {\n      continueFn = value => {\n        const canContinue = isResolved || !shouldPause();\n\n        if (canContinue) {\n          continueResolve(value);\n        }\n\n        return canContinue;\n      };\n\n      config.onPause == null ? void 0 : config.onPause();\n    }).then(() => {\n      continueFn = undefined;\n\n      if (!isResolved) {\n        config.onContinue == null ? void 0 : config.onContinue();\n      }\n    });\n  }; // Create loop function\n\n\n  const run = () => {\n    // Do nothing if already resolved\n    if (isResolved) {\n      return;\n    }\n\n    let promiseOrValue; // Execute query\n\n    try {\n      promiseOrValue = config.fn();\n    } catch (error) {\n      promiseOrValue = Promise.reject(error);\n    }\n\n    Promise.resolve(promiseOrValue).then(resolve).catch(error => {\n      var _config$retry, _config$retryDelay;\n\n      // Stop if the fetch is already resolved\n      if (isResolved) {\n        return;\n      } // Do we need to retry the request?\n\n\n      const retry = (_config$retry = config.retry) != null ? _config$retry : 3;\n      const retryDelay = (_config$retryDelay = config.retryDelay) != null ? _config$retryDelay : defaultRetryDelay;\n      const delay = typeof retryDelay === 'function' ? retryDelay(failureCount, error) : retryDelay;\n      const shouldRetry = retry === true || typeof retry === 'number' && failureCount < retry || typeof retry === 'function' && retry(failureCount, error);\n\n      if (isRetryCancelled || !shouldRetry) {\n        // We are done if the query does not need to be retried\n        reject(error);\n        return;\n      }\n\n      failureCount++; // Notify on fail\n\n      config.onFail == null ? void 0 : config.onFail(failureCount, error); // Delay\n\n      (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_2__.sleep)(delay) // Pause if the document is not visible or when the device is offline\n      .then(() => {\n        if (shouldPause()) {\n          return pause();\n        }\n\n        return;\n      }).then(() => {\n        if (isRetryCancelled) {\n          reject(error);\n        } else {\n          run();\n        }\n      });\n    });\n  }; // Start loop\n\n\n  if (canFetch(config.networkMode)) {\n    run();\n  } else {\n    pause().then(run);\n  }\n\n  return {\n    promise,\n    cancel,\n    continue: () => {\n      const didContinue = continueFn == null ? void 0 : continueFn();\n      return didContinue ? promise : Promise.resolve();\n    },\n    cancelRetry,\n    continueRetry\n  };\n}\n\n\n//# sourceMappingURL=retryer.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/lib/retryer.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/lib/subscribable.mjs":
/*!**********************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/lib/subscribable.mjs ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Subscribable: () => (/* binding */ Subscribable)\n/* harmony export */ });\nclass Subscribable {\n  constructor() {\n    this.listeners = new Set();\n    this.subscribe = this.subscribe.bind(this);\n  }\n\n  subscribe(listener) {\n    const identity = {\n      listener\n    };\n    this.listeners.add(identity);\n    this.onSubscribe();\n    return () => {\n      this.listeners.delete(identity);\n      this.onUnsubscribe();\n    };\n  }\n\n  hasListeners() {\n    return this.listeners.size > 0;\n  }\n\n  onSubscribe() {// Do nothing\n  }\n\n  onUnsubscribe() {// Do nothing\n  }\n\n}\n\n\n//# sourceMappingURL=subscribable.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRhbnN0YWNrL3F1ZXJ5LWNvcmUvYnVpbGQvbGliL3N1YnNjcmliYWJsZS5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUEsaUJBQWlCO0FBQ2pCOztBQUVBLG1CQUFtQjtBQUNuQjs7QUFFQTs7QUFFd0I7QUFDeEIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYmVya2FcXERlc2t0b3BcXFJFTkVST1xcbmV4dF9yZW5lcm9cXG5vZGVfbW9kdWxlc1xcQHRhbnN0YWNrXFxxdWVyeS1jb3JlXFxidWlsZFxcbGliXFxzdWJzY3JpYmFibGUubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImNsYXNzIFN1YnNjcmliYWJsZSB7XG4gIGNvbnN0cnVjdG9yKCkge1xuICAgIHRoaXMubGlzdGVuZXJzID0gbmV3IFNldCgpO1xuICAgIHRoaXMuc3Vic2NyaWJlID0gdGhpcy5zdWJzY3JpYmUuYmluZCh0aGlzKTtcbiAgfVxuXG4gIHN1YnNjcmliZShsaXN0ZW5lcikge1xuICAgIGNvbnN0IGlkZW50aXR5ID0ge1xuICAgICAgbGlzdGVuZXJcbiAgICB9O1xuICAgIHRoaXMubGlzdGVuZXJzLmFkZChpZGVudGl0eSk7XG4gICAgdGhpcy5vblN1YnNjcmliZSgpO1xuICAgIHJldHVybiAoKSA9PiB7XG4gICAgICB0aGlzLmxpc3RlbmVycy5kZWxldGUoaWRlbnRpdHkpO1xuICAgICAgdGhpcy5vblVuc3Vic2NyaWJlKCk7XG4gICAgfTtcbiAgfVxuXG4gIGhhc0xpc3RlbmVycygpIHtcbiAgICByZXR1cm4gdGhpcy5saXN0ZW5lcnMuc2l6ZSA+IDA7XG4gIH1cblxuICBvblN1YnNjcmliZSgpIHsvLyBEbyBub3RoaW5nXG4gIH1cblxuICBvblVuc3Vic2NyaWJlKCkgey8vIERvIG5vdGhpbmdcbiAgfVxuXG59XG5cbmV4cG9ydCB7IFN1YnNjcmliYWJsZSB9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9c3Vic2NyaWJhYmxlLm1qcy5tYXBcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/lib/subscribable.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/lib/utils.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/lib/utils.mjs ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   difference: () => (/* binding */ difference),\n/* harmony export */   functionalUpdate: () => (/* binding */ functionalUpdate),\n/* harmony export */   getAbortController: () => (/* binding */ getAbortController),\n/* harmony export */   hashQueryKey: () => (/* binding */ hashQueryKey),\n/* harmony export */   hashQueryKeyByOptions: () => (/* binding */ hashQueryKeyByOptions),\n/* harmony export */   isError: () => (/* binding */ isError),\n/* harmony export */   isPlainArray: () => (/* binding */ isPlainArray),\n/* harmony export */   isPlainObject: () => (/* binding */ isPlainObject),\n/* harmony export */   isQueryKey: () => (/* binding */ isQueryKey),\n/* harmony export */   isServer: () => (/* binding */ isServer),\n/* harmony export */   isValidTimeout: () => (/* binding */ isValidTimeout),\n/* harmony export */   matchMutation: () => (/* binding */ matchMutation),\n/* harmony export */   matchQuery: () => (/* binding */ matchQuery),\n/* harmony export */   noop: () => (/* binding */ noop),\n/* harmony export */   parseFilterArgs: () => (/* binding */ parseFilterArgs),\n/* harmony export */   parseMutationArgs: () => (/* binding */ parseMutationArgs),\n/* harmony export */   parseMutationFilterArgs: () => (/* binding */ parseMutationFilterArgs),\n/* harmony export */   parseQueryArgs: () => (/* binding */ parseQueryArgs),\n/* harmony export */   partialDeepEqual: () => (/* binding */ partialDeepEqual),\n/* harmony export */   partialMatchKey: () => (/* binding */ partialMatchKey),\n/* harmony export */   replaceAt: () => (/* binding */ replaceAt),\n/* harmony export */   replaceData: () => (/* binding */ replaceData),\n/* harmony export */   replaceEqualDeep: () => (/* binding */ replaceEqualDeep),\n/* harmony export */   scheduleMicrotask: () => (/* binding */ scheduleMicrotask),\n/* harmony export */   shallowEqualObjects: () => (/* binding */ shallowEqualObjects),\n/* harmony export */   sleep: () => (/* binding */ sleep),\n/* harmony export */   timeUntilStale: () => (/* binding */ timeUntilStale)\n/* harmony export */ });\n// TYPES\n// UTILS\nconst isServer = typeof window === 'undefined' || 'Deno' in window;\nfunction noop() {\n  return undefined;\n}\nfunction functionalUpdate(updater, input) {\n  return typeof updater === 'function' ? updater(input) : updater;\n}\nfunction isValidTimeout(value) {\n  return typeof value === 'number' && value >= 0 && value !== Infinity;\n}\nfunction difference(array1, array2) {\n  return array1.filter(x => !array2.includes(x));\n}\nfunction replaceAt(array, index, value) {\n  const copy = array.slice(0);\n  copy[index] = value;\n  return copy;\n}\nfunction timeUntilStale(updatedAt, staleTime) {\n  return Math.max(updatedAt + (staleTime || 0) - Date.now(), 0);\n}\nfunction parseQueryArgs(arg1, arg2, arg3) {\n  if (!isQueryKey(arg1)) {\n    return arg1;\n  }\n\n  if (typeof arg2 === 'function') {\n    return { ...arg3,\n      queryKey: arg1,\n      queryFn: arg2\n    };\n  }\n\n  return { ...arg2,\n    queryKey: arg1\n  };\n}\nfunction parseMutationArgs(arg1, arg2, arg3) {\n  if (isQueryKey(arg1)) {\n    if (typeof arg2 === 'function') {\n      return { ...arg3,\n        mutationKey: arg1,\n        mutationFn: arg2\n      };\n    }\n\n    return { ...arg2,\n      mutationKey: arg1\n    };\n  }\n\n  if (typeof arg1 === 'function') {\n    return { ...arg2,\n      mutationFn: arg1\n    };\n  }\n\n  return { ...arg1\n  };\n}\nfunction parseFilterArgs(arg1, arg2, arg3) {\n  return isQueryKey(arg1) ? [{ ...arg2,\n    queryKey: arg1\n  }, arg3] : [arg1 || {}, arg2];\n}\nfunction parseMutationFilterArgs(arg1, arg2, arg3) {\n  return isQueryKey(arg1) ? [{ ...arg2,\n    mutationKey: arg1\n  }, arg3] : [arg1 || {}, arg2];\n}\nfunction matchQuery(filters, query) {\n  const {\n    type = 'all',\n    exact,\n    fetchStatus,\n    predicate,\n    queryKey,\n    stale\n  } = filters;\n\n  if (isQueryKey(queryKey)) {\n    if (exact) {\n      if (query.queryHash !== hashQueryKeyByOptions(queryKey, query.options)) {\n        return false;\n      }\n    } else if (!partialMatchKey(query.queryKey, queryKey)) {\n      return false;\n    }\n  }\n\n  if (type !== 'all') {\n    const isActive = query.isActive();\n\n    if (type === 'active' && !isActive) {\n      return false;\n    }\n\n    if (type === 'inactive' && isActive) {\n      return false;\n    }\n  }\n\n  if (typeof stale === 'boolean' && query.isStale() !== stale) {\n    return false;\n  }\n\n  if (typeof fetchStatus !== 'undefined' && fetchStatus !== query.state.fetchStatus) {\n    return false;\n  }\n\n  if (predicate && !predicate(query)) {\n    return false;\n  }\n\n  return true;\n}\nfunction matchMutation(filters, mutation) {\n  const {\n    exact,\n    fetching,\n    predicate,\n    mutationKey\n  } = filters;\n\n  if (isQueryKey(mutationKey)) {\n    if (!mutation.options.mutationKey) {\n      return false;\n    }\n\n    if (exact) {\n      if (hashQueryKey(mutation.options.mutationKey) !== hashQueryKey(mutationKey)) {\n        return false;\n      }\n    } else if (!partialMatchKey(mutation.options.mutationKey, mutationKey)) {\n      return false;\n    }\n  }\n\n  if (typeof fetching === 'boolean' && mutation.state.status === 'loading' !== fetching) {\n    return false;\n  }\n\n  if (predicate && !predicate(mutation)) {\n    return false;\n  }\n\n  return true;\n}\nfunction hashQueryKeyByOptions(queryKey, options) {\n  const hashFn = (options == null ? void 0 : options.queryKeyHashFn) || hashQueryKey;\n  return hashFn(queryKey);\n}\n/**\n * Default query keys hash function.\n * Hashes the value into a stable hash.\n */\n\nfunction hashQueryKey(queryKey) {\n  return JSON.stringify(queryKey, (_, val) => isPlainObject(val) ? Object.keys(val).sort().reduce((result, key) => {\n    result[key] = val[key];\n    return result;\n  }, {}) : val);\n}\n/**\n * Checks if key `b` partially matches with key `a`.\n */\n\nfunction partialMatchKey(a, b) {\n  return partialDeepEqual(a, b);\n}\n/**\n * Checks if `b` partially matches with `a`.\n */\n\nfunction partialDeepEqual(a, b) {\n  if (a === b) {\n    return true;\n  }\n\n  if (typeof a !== typeof b) {\n    return false;\n  }\n\n  if (a && b && typeof a === 'object' && typeof b === 'object') {\n    return !Object.keys(b).some(key => !partialDeepEqual(a[key], b[key]));\n  }\n\n  return false;\n}\n/**\n * This function returns `a` if `b` is deeply equal.\n * If not, it will replace any deeply equal children of `b` with those of `a`.\n * This can be used for structural sharing between JSON values for example.\n */\n\nfunction replaceEqualDeep(a, b) {\n  if (a === b) {\n    return a;\n  }\n\n  const array = isPlainArray(a) && isPlainArray(b);\n\n  if (array || isPlainObject(a) && isPlainObject(b)) {\n    const aSize = array ? a.length : Object.keys(a).length;\n    const bItems = array ? b : Object.keys(b);\n    const bSize = bItems.length;\n    const copy = array ? [] : {};\n    let equalItems = 0;\n\n    for (let i = 0; i < bSize; i++) {\n      const key = array ? i : bItems[i];\n      copy[key] = replaceEqualDeep(a[key], b[key]);\n\n      if (copy[key] === a[key]) {\n        equalItems++;\n      }\n    }\n\n    return aSize === bSize && equalItems === aSize ? a : copy;\n  }\n\n  return b;\n}\n/**\n * Shallow compare objects. Only works with objects that always have the same properties.\n */\n\nfunction shallowEqualObjects(a, b) {\n  if (a && !b || b && !a) {\n    return false;\n  }\n\n  for (const key in a) {\n    if (a[key] !== b[key]) {\n      return false;\n    }\n  }\n\n  return true;\n}\nfunction isPlainArray(value) {\n  return Array.isArray(value) && value.length === Object.keys(value).length;\n} // Copied from: https://github.com/jonschlinkert/is-plain-object\n\nfunction isPlainObject(o) {\n  if (!hasObjectPrototype(o)) {\n    return false;\n  } // If has modified constructor\n\n\n  const ctor = o.constructor;\n\n  if (typeof ctor === 'undefined') {\n    return true;\n  } // If has modified prototype\n\n\n  const prot = ctor.prototype;\n\n  if (!hasObjectPrototype(prot)) {\n    return false;\n  } // If constructor does not have an Object-specific method\n\n\n  if (!prot.hasOwnProperty('isPrototypeOf')) {\n    return false;\n  } // Most likely a plain Object\n\n\n  return true;\n}\n\nfunction hasObjectPrototype(o) {\n  return Object.prototype.toString.call(o) === '[object Object]';\n}\n\nfunction isQueryKey(value) {\n  return Array.isArray(value);\n}\nfunction isError(value) {\n  return value instanceof Error;\n}\nfunction sleep(timeout) {\n  return new Promise(resolve => {\n    setTimeout(resolve, timeout);\n  });\n}\n/**\n * Schedules a microtask.\n * This can be useful to schedule state updates after rendering.\n */\n\nfunction scheduleMicrotask(callback) {\n  sleep(0).then(callback);\n}\nfunction getAbortController() {\n  if (typeof AbortController === 'function') {\n    return new AbortController();\n  }\n\n  return;\n}\nfunction replaceData(prevData, data, options) {\n  // Use prev data if an isDataEqual function is defined and returns `true`\n  if (options.isDataEqual != null && options.isDataEqual(prevData, data)) {\n    return prevData;\n  } else if (typeof options.structuralSharing === 'function') {\n    return options.structuralSharing(prevData, data);\n  } else if (options.structuralSharing !== false) {\n    // Structurally share data between prev and new data if needed\n    return replaceEqualDeep(prevData, data);\n  }\n\n  return data;\n}\n\n\n//# sourceMappingURL=utils.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/lib/utils.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/react-query/build/lib/QueryClientProvider.mjs":
/*!******************************************************************************!*\
  !*** ./node_modules/@tanstack/react-query/build/lib/QueryClientProvider.mjs ***!
  \******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryClientProvider: () => (/* binding */ QueryClientProvider),\n/* harmony export */   defaultContext: () => (/* binding */ defaultContext),\n/* harmony export */   useQueryClient: () => (/* binding */ useQueryClient)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* __next_internal_client_entry_do_not_use__ QueryClientProvider,defaultContext,useQueryClient auto */ \nconst defaultContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(undefined);\nconst QueryClientSharingContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(false); // If we are given a context, we will use it.\n// Otherwise, if contextSharing is on, we share the first and at least one\n// instance of the context across the window\n// to ensure that if React Query is used across\n// different bundles or microfrontends they will\n// all use the same **instance** of context, regardless\n// of module scoping.\nfunction getQueryClientContext(context, contextSharing) {\n    if (context) {\n        return context;\n    }\n    if (contextSharing && \"undefined\" !== 'undefined') {}\n    return defaultContext;\n}\nconst useQueryClient = ({ context } = {})=>{\n    const queryClient = react__WEBPACK_IMPORTED_MODULE_0__.useContext(getQueryClientContext(context, react__WEBPACK_IMPORTED_MODULE_0__.useContext(QueryClientSharingContext)));\n    if (!queryClient) {\n        throw new Error('No QueryClient set, use QueryClientProvider to set one');\n    }\n    return queryClient;\n};\nconst QueryClientProvider = ({ client, children, context, contextSharing = false })=>{\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"QueryClientProvider.useEffect\": ()=>{\n            client.mount();\n            return ({\n                \"QueryClientProvider.useEffect\": ()=>{\n                    client.unmount();\n                }\n            })[\"QueryClientProvider.useEffect\"];\n        }\n    }[\"QueryClientProvider.useEffect\"], [\n        client\n    ]);\n    if ( true && contextSharing) {\n        client.getLogger().error(\"The contextSharing option has been deprecated and will be removed in the next major version\");\n    }\n    const Context = getQueryClientContext(context, contextSharing);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(QueryClientSharingContext.Provider, {\n        value: !context && contextSharing\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(Context.Provider, {\n        value: client\n    }, children));\n};\n //# sourceMappingURL=QueryClientProvider.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/react-query/build/lib/QueryClientProvider.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/react-query/build/lib/QueryErrorResetBoundary.mjs":
/*!**********************************************************************************!*\
  !*** ./node_modules/@tanstack/react-query/build/lib/QueryErrorResetBoundary.mjs ***!
  \**********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryErrorResetBoundary: () => (/* binding */ QueryErrorResetBoundary),\n/* harmony export */   useQueryErrorResetBoundary: () => (/* binding */ useQueryErrorResetBoundary)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* __next_internal_client_entry_do_not_use__ QueryErrorResetBoundary,useQueryErrorResetBoundary auto */ \nfunction createValue() {\n    let isReset = false;\n    return {\n        clearReset: ()=>{\n            isReset = false;\n        },\n        reset: ()=>{\n            isReset = true;\n        },\n        isReset: ()=>{\n            return isReset;\n        }\n    };\n}\nconst QueryErrorResetBoundaryContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(createValue()); // HOOK\nconst useQueryErrorResetBoundary = ()=>react__WEBPACK_IMPORTED_MODULE_0__.useContext(QueryErrorResetBoundaryContext); // COMPONENT\nconst QueryErrorResetBoundary = ({ children })=>{\n    const [value] = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n        \"QueryErrorResetBoundary.useState\": ()=>createValue()\n    }[\"QueryErrorResetBoundary.useState\"]);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(QueryErrorResetBoundaryContext.Provider, {\n        value: value\n    }, typeof children === 'function' ? children(value) : children);\n};\n //# sourceMappingURL=QueryErrorResetBoundary.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/react-query/build/lib/QueryErrorResetBoundary.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/react-query/build/lib/errorBoundaryUtils.mjs":
/*!*****************************************************************************!*\
  !*** ./node_modules/@tanstack/react-query/build/lib/errorBoundaryUtils.mjs ***!
  \*****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ensurePreventErrorBoundaryRetry: () => (/* binding */ ensurePreventErrorBoundaryRetry),\n/* harmony export */   getHasError: () => (/* binding */ getHasError),\n/* harmony export */   useClearResetErrorBoundary: () => (/* binding */ useClearResetErrorBoundary)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _utils_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.mjs */ \"(ssr)/./node_modules/@tanstack/react-query/build/lib/utils.mjs\");\n/* __next_internal_client_entry_do_not_use__ ensurePreventErrorBoundaryRetry,getHasError,useClearResetErrorBoundary auto */ \n\nconst ensurePreventErrorBoundaryRetry = (options, errorResetBoundary)=>{\n    if (options.suspense || options.useErrorBoundary) {\n        // Prevent retrying failed query if the error boundary has not been reset yet\n        if (!errorResetBoundary.isReset()) {\n            options.retryOnMount = false;\n        }\n    }\n};\nconst useClearResetErrorBoundary = (errorResetBoundary)=>{\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useClearResetErrorBoundary.useEffect\": ()=>{\n            errorResetBoundary.clearReset();\n        }\n    }[\"useClearResetErrorBoundary.useEffect\"], [\n        errorResetBoundary\n    ]);\n};\nconst getHasError = ({ result, errorResetBoundary, useErrorBoundary, query })=>{\n    return result.isError && !errorResetBoundary.isReset() && !result.isFetching && (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_1__.shouldThrowError)(useErrorBoundary, [\n        result.error,\n        query\n    ]);\n};\n //# sourceMappingURL=errorBoundaryUtils.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRhbnN0YWNrL3JlYWN0LXF1ZXJ5L2J1aWxkL2xpYi9lcnJvckJvdW5kYXJ5VXRpbHMubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0lBNEJFO1FBQ0U7UUFDQTs7UUFFQztJQUNGO0FBQ0Y7QUFFWUEsTUFBQUEsNkJBQUFBLENBQUFBOzs7WUFJVEMsbUJBQUFBLFVBQUFBOzs7OztBQUVIO0FBRU0scUVBVUxDLEtBQUFBLEVBSkE7Ozs7O0FBc0JEIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJlcmthXFxEZXNrdG9wXFxzcmNcXGVycm9yQm91bmRhcnlVdGlscy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gJ3JlYWN0J1xuaW1wb3J0IHsgc2hvdWxkVGhyb3dFcnJvciB9IGZyb20gJy4vdXRpbHMnXG5pbXBvcnQgdHlwZSB7XG4gIERlZmF1bHRlZFF1ZXJ5T2JzZXJ2ZXJPcHRpb25zLFxuICBRdWVyeSxcbiAgUXVlcnlLZXksXG4gIFF1ZXJ5T2JzZXJ2ZXJSZXN1bHQsXG4gIFVzZUVycm9yQm91bmRhcnksXG59IGZyb20gJ0B0YW5zdGFjay9xdWVyeS1jb3JlJ1xuaW1wb3J0IHR5cGUgeyBRdWVyeUVycm9yUmVzZXRCb3VuZGFyeVZhbHVlIH0gZnJvbSAnLi9RdWVyeUVycm9yUmVzZXRCb3VuZGFyeSdcblxuZXhwb3J0IGNvbnN0IGVuc3VyZVByZXZlbnRFcnJvckJvdW5kYXJ5UmV0cnkgPSA8XG4gIFRRdWVyeUZuRGF0YSxcbiAgVEVycm9yLFxuICBURGF0YSxcbiAgVFF1ZXJ5RGF0YSxcbiAgVFF1ZXJ5S2V5IGV4dGVuZHMgUXVlcnlLZXksXG4+KFxuICBvcHRpb25zOiBEZWZhdWx0ZWRRdWVyeU9ic2VydmVyT3B0aW9uczxcbiAgICBUUXVlcnlGbkRhdGEsXG4gICAgVEVycm9yLFxuICAgIFREYXRhLFxuICAgIFRRdWVyeURhdGEsXG4gICAgVFF1ZXJ5S2V5XG4gID4sXG4gIGVycm9yUmVzZXRCb3VuZGFyeTogUXVlcnlFcnJvclJlc2V0Qm91bmRhcnlWYWx1ZSxcbikgPT4ge1xuICBpZiAob3B0aW9ucy5zdXNwZW5zZSB8fCBvcHRpb25zLnVzZUVycm9yQm91bmRhcnkpIHtcbiAgICAvLyBQcmV2ZW50IHJldHJ5aW5nIGZhaWxlZCBxdWVyeSBpZiB0aGUgZXJyb3IgYm91bmRhcnkgaGFzIG5vdCBiZWVuIHJlc2V0IHlldFxuICAgIGlmICghZXJyb3JSZXNldEJvdW5kYXJ5LmlzUmVzZXQoKSkge1xuICAgICAgb3B0aW9ucy5yZXRyeU9uTW91bnQgPSBmYWxzZVxuICAgIH1cbiAgfVxufVxuXG5leHBvcnQgY29uc3QgdXNlQ2xlYXJSZXNldEVycm9yQm91bmRhcnkgPSAoXG4gIGVycm9yUmVzZXRCb3VuZGFyeTogUXVlcnlFcnJvclJlc2V0Qm91bmRhcnlWYWx1ZSxcbikgPT4ge1xuICBSZWFjdC51c2VFZmZlY3QoKCkgPT4ge1xuICAgIGVycm9yUmVzZXRCb3VuZGFyeS5jbGVhclJlc2V0KClcbiAgfSwgW2Vycm9yUmVzZXRCb3VuZGFyeV0pXG59XG5cbmV4cG9ydCBjb25zdCBnZXRIYXNFcnJvciA9IDxcbiAgVERhdGEsXG4gIFRFcnJvcixcbiAgVFF1ZXJ5Rm5EYXRhLFxuICBUUXVlcnlEYXRhLFxuICBUUXVlcnlLZXkgZXh0ZW5kcyBRdWVyeUtleSxcbj4oe1xuICByZXN1bHQsXG4gIGVycm9yUmVzZXRCb3VuZGFyeSxcbiAgdXNlRXJyb3JCb3VuZGFyeSxcbiAgcXVlcnksXG59OiB7XG4gIHJlc3VsdDogUXVlcnlPYnNlcnZlclJlc3VsdDxURGF0YSwgVEVycm9yPlxuICBlcnJvclJlc2V0Qm91bmRhcnk6IFF1ZXJ5RXJyb3JSZXNldEJvdW5kYXJ5VmFsdWVcbiAgdXNlRXJyb3JCb3VuZGFyeTogVXNlRXJyb3JCb3VuZGFyeTxcbiAgICBUUXVlcnlGbkRhdGEsXG4gICAgVEVycm9yLFxuICAgIFRRdWVyeURhdGEsXG4gICAgVFF1ZXJ5S2V5XG4gID5cbiAgcXVlcnk6IFF1ZXJ5PFRRdWVyeUZuRGF0YSwgVEVycm9yLCBUUXVlcnlEYXRhLCBUUXVlcnlLZXk+XG59KSA9PiB7XG4gIHJldHVybiAoXG4gICAgcmVzdWx0LmlzRXJyb3IgJiZcbiAgICAhZXJyb3JSZXNldEJvdW5kYXJ5LmlzUmVzZXQoKSAmJlxuICAgICFyZXN1bHQuaXNGZXRjaGluZyAmJlxuICAgIHNob3VsZFRocm93RXJyb3IodXNlRXJyb3JCb3VuZGFyeSwgW3Jlc3VsdC5lcnJvciwgcXVlcnldKVxuICApXG59XG4iXSwibmFtZXMiOlsidXNlQ2xlYXJSZXNldEVycm9yQm91bmRhcnkiLCJlcnJvclJlc2V0Qm91bmRhcnkiLCJxdWVyeSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/react-query/build/lib/errorBoundaryUtils.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/react-query/build/lib/isRestoring.mjs":
/*!**********************************************************************!*\
  !*** ./node_modules/@tanstack/react-query/build/lib/isRestoring.mjs ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   IsRestoringProvider: () => (/* binding */ IsRestoringProvider),\n/* harmony export */   useIsRestoring: () => (/* binding */ useIsRestoring)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* __next_internal_client_entry_do_not_use__ IsRestoringProvider,useIsRestoring auto */ \nconst IsRestoringContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(false);\nconst useIsRestoring = ()=>react__WEBPACK_IMPORTED_MODULE_0__.useContext(IsRestoringContext);\nconst IsRestoringProvider = IsRestoringContext.Provider;\n //# sourceMappingURL=isRestoring.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRhbnN0YWNrL3JlYWN0LXF1ZXJ5L2J1aWxkL2xpYi9pc1Jlc3RvcmluZy5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUdBO0FBRU87QUFDTUEsTUFBQUEsc0JBQUFBLG1CQUFBQSxRQUFBQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxiZXJrYVxcRGVza3RvcFxcc3JjXFxpc1Jlc3RvcmluZy50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tICdyZWFjdCdcblxuY29uc3QgSXNSZXN0b3JpbmdDb250ZXh0ID0gUmVhY3QuY3JlYXRlQ29udGV4dChmYWxzZSlcblxuZXhwb3J0IGNvbnN0IHVzZUlzUmVzdG9yaW5nID0gKCkgPT4gUmVhY3QudXNlQ29udGV4dChJc1Jlc3RvcmluZ0NvbnRleHQpXG5leHBvcnQgY29uc3QgSXNSZXN0b3JpbmdQcm92aWRlciA9IElzUmVzdG9yaW5nQ29udGV4dC5Qcm92aWRlclxuIl0sIm5hbWVzIjpbIklzUmVzdG9yaW5nUHJvdmlkZXIiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/react-query/build/lib/isRestoring.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/react-query/build/lib/suspense.mjs":
/*!*******************************************************************!*\
  !*** ./node_modules/@tanstack/react-query/build/lib/suspense.mjs ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ensureStaleTime: () => (/* binding */ ensureStaleTime),\n/* harmony export */   fetchOptimistic: () => (/* binding */ fetchOptimistic),\n/* harmony export */   shouldSuspend: () => (/* binding */ shouldSuspend),\n/* harmony export */   willFetch: () => (/* binding */ willFetch)\n/* harmony export */ });\nconst ensureStaleTime = defaultedOptions => {\n  if (defaultedOptions.suspense) {\n    // Always set stale time when using suspense to prevent\n    // fetching again when directly mounting after suspending\n    if (typeof defaultedOptions.staleTime !== 'number') {\n      defaultedOptions.staleTime = 1000;\n    }\n  }\n};\nconst willFetch = (result, isRestoring) => result.isLoading && result.isFetching && !isRestoring;\nconst shouldSuspend = (defaultedOptions, result, isRestoring) => (defaultedOptions == null ? void 0 : defaultedOptions.suspense) && willFetch(result, isRestoring);\nconst fetchOptimistic = (defaultedOptions, observer, errorResetBoundary) => observer.fetchOptimistic(defaultedOptions).then(({\n  data\n}) => {\n  defaultedOptions.onSuccess == null ? void 0 : defaultedOptions.onSuccess(data);\n  defaultedOptions.onSettled == null ? void 0 : defaultedOptions.onSettled(data, null);\n}).catch(error => {\n  errorResetBoundary.clearReset();\n  defaultedOptions.onError == null ? void 0 : defaultedOptions.onError(error);\n  defaultedOptions.onSettled == null ? void 0 : defaultedOptions.onSettled(undefined, error);\n});\n\n\n//# sourceMappingURL=suspense.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/react-query/build/lib/suspense.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/react-query/build/lib/useBaseQuery.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/@tanstack/react-query/build/lib/useBaseQuery.mjs ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useBaseQuery: () => (/* binding */ useBaseQuery)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _tanstack_query_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tanstack/query-core */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/notifyManager.mjs\");\n/* harmony import */ var _useSyncExternalStore_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./useSyncExternalStore.mjs */ \"(ssr)/./node_modules/@tanstack/react-query/build/lib/useSyncExternalStore.mjs\");\n/* harmony import */ var _QueryErrorResetBoundary_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./QueryErrorResetBoundary.mjs */ \"(ssr)/./node_modules/@tanstack/react-query/build/lib/QueryErrorResetBoundary.mjs\");\n/* harmony import */ var _QueryClientProvider_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./QueryClientProvider.mjs */ \"(ssr)/./node_modules/@tanstack/react-query/build/lib/QueryClientProvider.mjs\");\n/* harmony import */ var _isRestoring_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./isRestoring.mjs */ \"(ssr)/./node_modules/@tanstack/react-query/build/lib/isRestoring.mjs\");\n/* harmony import */ var _errorBoundaryUtils_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./errorBoundaryUtils.mjs */ \"(ssr)/./node_modules/@tanstack/react-query/build/lib/errorBoundaryUtils.mjs\");\n/* harmony import */ var _suspense_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./suspense.mjs */ \"(ssr)/./node_modules/@tanstack/react-query/build/lib/suspense.mjs\");\n/* __next_internal_client_entry_do_not_use__ useBaseQuery auto */ \n\n\n\n\n\n\n\nfunction useBaseQuery(options, Observer) {\n    const queryClient = (0,_QueryClientProvider_mjs__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)({\n        context: options.context\n    });\n    const isRestoring = (0,_isRestoring_mjs__WEBPACK_IMPORTED_MODULE_2__.useIsRestoring)();\n    const errorResetBoundary = (0,_QueryErrorResetBoundary_mjs__WEBPACK_IMPORTED_MODULE_3__.useQueryErrorResetBoundary)();\n    const defaultedOptions = queryClient.defaultQueryOptions(options); // Make sure results are optimistically set in fetching state before subscribing or updating options\n    defaultedOptions._optimisticResults = isRestoring ? 'isRestoring' : 'optimistic'; // Include callbacks in batch renders\n    if (defaultedOptions.onError) {\n        defaultedOptions.onError = _tanstack_query_core__WEBPACK_IMPORTED_MODULE_4__.notifyManager.batchCalls(defaultedOptions.onError);\n    }\n    if (defaultedOptions.onSuccess) {\n        defaultedOptions.onSuccess = _tanstack_query_core__WEBPACK_IMPORTED_MODULE_4__.notifyManager.batchCalls(defaultedOptions.onSuccess);\n    }\n    if (defaultedOptions.onSettled) {\n        defaultedOptions.onSettled = _tanstack_query_core__WEBPACK_IMPORTED_MODULE_4__.notifyManager.batchCalls(defaultedOptions.onSettled);\n    }\n    (0,_suspense_mjs__WEBPACK_IMPORTED_MODULE_5__.ensureStaleTime)(defaultedOptions);\n    (0,_errorBoundaryUtils_mjs__WEBPACK_IMPORTED_MODULE_6__.ensurePreventErrorBoundaryRetry)(defaultedOptions, errorResetBoundary);\n    (0,_errorBoundaryUtils_mjs__WEBPACK_IMPORTED_MODULE_6__.useClearResetErrorBoundary)(errorResetBoundary);\n    const [observer] = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n        \"useBaseQuery.useState\": ()=>new Observer(queryClient, defaultedOptions)\n    }[\"useBaseQuery.useState\"]);\n    const result = observer.getOptimisticResult(defaultedOptions);\n    (0,_useSyncExternalStore_mjs__WEBPACK_IMPORTED_MODULE_7__.useSyncExternalStore)(react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"useBaseQuery.useSyncExternalStore.useCallback\": (onStoreChange)=>{\n            const unsubscribe = isRestoring ? ({\n                \"useBaseQuery.useSyncExternalStore.useCallback\": ()=>undefined\n            })[\"useBaseQuery.useSyncExternalStore.useCallback\"] : observer.subscribe(_tanstack_query_core__WEBPACK_IMPORTED_MODULE_4__.notifyManager.batchCalls(onStoreChange)); // Update result to make sure we did not miss any query updates\n            // between creating the observer and subscribing to it.\n            observer.updateResult();\n            return unsubscribe;\n        }\n    }[\"useBaseQuery.useSyncExternalStore.useCallback\"], [\n        observer,\n        isRestoring\n    ]), {\n        \"useBaseQuery.useSyncExternalStore\": ()=>observer.getCurrentResult()\n    }[\"useBaseQuery.useSyncExternalStore\"], {\n        \"useBaseQuery.useSyncExternalStore\": ()=>observer.getCurrentResult()\n    }[\"useBaseQuery.useSyncExternalStore\"]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useBaseQuery.useEffect\": ()=>{\n            // Do not notify on updates because of changes in the options because\n            // these changes should already be reflected in the optimistic result.\n            observer.setOptions(defaultedOptions, {\n                listeners: false\n            });\n        }\n    }[\"useBaseQuery.useEffect\"], [\n        defaultedOptions,\n        observer\n    ]); // Handle suspense\n    if ((0,_suspense_mjs__WEBPACK_IMPORTED_MODULE_5__.shouldSuspend)(defaultedOptions, result, isRestoring)) {\n        throw (0,_suspense_mjs__WEBPACK_IMPORTED_MODULE_5__.fetchOptimistic)(defaultedOptions, observer, errorResetBoundary);\n    } // Handle error boundary\n    if ((0,_errorBoundaryUtils_mjs__WEBPACK_IMPORTED_MODULE_6__.getHasError)({\n        result,\n        errorResetBoundary,\n        useErrorBoundary: defaultedOptions.useErrorBoundary,\n        query: observer.getCurrentQuery()\n    })) {\n        throw result.error;\n    } // Handle result property usage tracking\n    return !defaultedOptions.notifyOnChangeProps ? observer.trackResult(result) : result;\n}\n //# sourceMappingURL=useBaseQuery.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/react-query/build/lib/useBaseQuery.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/react-query/build/lib/useQuery.mjs":
/*!*******************************************************************!*\
  !*** ./node_modules/@tanstack/react-query/build/lib/useQuery.mjs ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useQuery: () => (/* binding */ useQuery)\n/* harmony export */ });\n/* harmony import */ var _tanstack_query_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tanstack/query-core */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/utils.mjs\");\n/* harmony import */ var _tanstack_query_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tanstack/query-core */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/queryObserver.mjs\");\n/* harmony import */ var _useBaseQuery_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./useBaseQuery.mjs */ \"(ssr)/./node_modules/@tanstack/react-query/build/lib/useBaseQuery.mjs\");\n/* __next_internal_client_entry_do_not_use__ useQuery auto */ \n\nfunction useQuery(arg1, arg2, arg3) {\n    const parsedOptions = (0,_tanstack_query_core__WEBPACK_IMPORTED_MODULE_0__.parseQueryArgs)(arg1, arg2, arg3);\n    return (0,_useBaseQuery_mjs__WEBPACK_IMPORTED_MODULE_1__.useBaseQuery)(parsedOptions, _tanstack_query_core__WEBPACK_IMPORTED_MODULE_2__.QueryObserver);\n}\n //# sourceMappingURL=useQuery.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/react-query/build/lib/useQuery.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/react-query/build/lib/useSyncExternalStore.mjs":
/*!*******************************************************************************!*\
  !*** ./node_modules/@tanstack/react-query/build/lib/useSyncExternalStore.mjs ***!
  \*******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSyncExternalStore: () => (/* binding */ useSyncExternalStore)\n/* harmony export */ });\n/* harmony import */ var use_sync_external_store_shim_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! use-sync-external-store/shim/index.js */ \"(ssr)/./node_modules/use-sync-external-store/shim/index.js\");\n/* __next_internal_client_entry_do_not_use__ useSyncExternalStore auto */ \nconst useSyncExternalStore = use_sync_external_store_shim_index_js__WEBPACK_IMPORTED_MODULE_0__.useSyncExternalStore;\n //# sourceMappingURL=useSyncExternalStore.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRhbnN0YWNrL3JlYWN0LXF1ZXJ5L2J1aWxkL2xpYi91c2VTeW5jRXh0ZXJuYWxTdG9yZS5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBSU8iLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYmVya2FcXERlc2t0b3BcXHNyY1xcdXNlU3luY0V4dGVybmFsU3RvcmUudHMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG4vLyBUZW1wb3Jhcnkgd29ya2Fyb3VuZCBkdWUgdG8gYW4gaXNzdWUgd2l0aCByZWFjdC1uYXRpdmUgdVNFUyAtIGh0dHBzOi8vZ2l0aHViLmNvbS9UYW5TdGFjay9xdWVyeS9wdWxsLzM2MDFcbmltcG9ydCB7IHVzZVN5bmNFeHRlcm5hbFN0b3JlIGFzIHVTRVMgfSBmcm9tICd1c2Utc3luYy1leHRlcm5hbC1zdG9yZS9zaGltL2luZGV4LmpzJ1xuXG5leHBvcnQgY29uc3QgdXNlU3luY0V4dGVybmFsU3RvcmUgPSB1U0VTXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/react-query/build/lib/useSyncExternalStore.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/react-query/build/lib/utils.mjs":
/*!****************************************************************!*\
  !*** ./node_modules/@tanstack/react-query/build/lib/utils.mjs ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   shouldThrowError: () => (/* binding */ shouldThrowError)\n/* harmony export */ });\nfunction shouldThrowError(_useErrorBoundary, params) {\n  // Allow useErrorBoundary function to override throwing behavior on a per-error basis\n  if (typeof _useErrorBoundary === 'function') {\n    return _useErrorBoundary(...params);\n  }\n\n  return !!_useErrorBoundary;\n}\n\n\n//# sourceMappingURL=utils.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRhbnN0YWNrL3JlYWN0LXF1ZXJ5L2J1aWxkL2xpYi91dGlscy5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFNEI7QUFDNUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYmVya2FcXERlc2t0b3BcXFJFTkVST1xcbmV4dF9yZW5lcm9cXG5vZGVfbW9kdWxlc1xcQHRhbnN0YWNrXFxyZWFjdC1xdWVyeVxcYnVpbGRcXGxpYlxcdXRpbHMubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImZ1bmN0aW9uIHNob3VsZFRocm93RXJyb3IoX3VzZUVycm9yQm91bmRhcnksIHBhcmFtcykge1xuICAvLyBBbGxvdyB1c2VFcnJvckJvdW5kYXJ5IGZ1bmN0aW9uIHRvIG92ZXJyaWRlIHRocm93aW5nIGJlaGF2aW9yIG9uIGEgcGVyLWVycm9yIGJhc2lzXG4gIGlmICh0eXBlb2YgX3VzZUVycm9yQm91bmRhcnkgPT09ICdmdW5jdGlvbicpIHtcbiAgICByZXR1cm4gX3VzZUVycm9yQm91bmRhcnkoLi4ucGFyYW1zKTtcbiAgfVxuXG4gIHJldHVybiAhIV91c2VFcnJvckJvdW5kYXJ5O1xufVxuXG5leHBvcnQgeyBzaG91bGRUaHJvd0Vycm9yIH07XG4vLyMgc291cmNlTWFwcGluZ1VSTD11dGlscy5tanMubWFwXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/react-query/build/lib/utils.mjs\n");

/***/ })

};
;