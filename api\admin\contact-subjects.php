<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

require_once __DIR__ . '/config/database.php';
require_once __DIR__ . '/config/auth.php';

// Check authentication
if (!isAuthenticated()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Unauthorized']);
    exit;
}

function sanitizeInput($input) {
    return htmlspecialchars(strip_tags(trim($input)));
}

try {
    $db = new Database();
    $method = $_SERVER['REQUEST_METHOD'];

    switch ($method) {
        case 'GET':
            // Get all contact subjects for admin
            $sql = "SELECT id, name_tr, name_en, status, sort_order, created_at, updated_at
                    FROM contact_subjects 
                    ORDER BY sort_order, name_tr";
            $subjects = $db->fetchAll($sql);
            
            echo json_encode([
                'success' => true,
                'data' => $subjects
            ]);
            break;

        case 'POST':
            // Create new subject
            $name_tr = sanitizeInput($_POST['name_tr'] ?? '');
            $name_en = sanitizeInput($_POST['name_en'] ?? '');
            $sort_order = (int)($_POST['sort_order'] ?? 0);
            
            if (empty($name_tr)) {
                echo json_encode(['success' => false, 'message' => 'Türkçe konu adı gerekli']);
                exit;
            }
            
            // Get next sort order if not provided
            if ($sort_order <= 0) {
                $maxOrder = $db->fetch("SELECT MAX(sort_order) as max_order FROM contact_subjects");
                $sort_order = ($maxOrder['max_order'] ?? 0) + 1;
            }
            
            $sql = "INSERT INTO contact_subjects (name_tr, name_en, sort_order, status) 
                    VALUES (:name_tr, :name_en, :sort_order, 'active')";
            
            $params = [
                'name_tr' => $name_tr,
                'name_en' => $name_en,
                'sort_order' => $sort_order
            ];
            
            if ($db->query($sql, $params)) {
                echo json_encode(['success' => true, 'message' => 'Konu başarıyla eklendi']);
            } else {
                echo json_encode(['success' => false, 'message' => 'Konu eklenirken hata oluştu']);
            }
            break;

        case 'PUT':
            // Update subject
            $input = json_decode(file_get_contents("php://input"), true);
            
            $id = (int)($input['id'] ?? 0);
            $name_tr = sanitizeInput($input['name_tr'] ?? '');
            $name_en = sanitizeInput($input['name_en'] ?? '');
            $sort_order = (int)($input['sort_order'] ?? 0);
            
            if (!$id || empty($name_tr)) {
                echo json_encode(['success' => false, 'message' => 'ID ve Türkçe konu adı gerekli']);
                exit;
            }
            
            $sql = "UPDATE contact_subjects SET 
                    name_tr = :name_tr, 
                    name_en = :name_en, 
                    sort_order = :sort_order,
                    updated_at = CURRENT_TIMESTAMP
                    WHERE id = :id";
            
            $params = [
                'id' => $id,
                'name_tr' => $name_tr,
                'name_en' => $name_en,
                'sort_order' => $sort_order
            ];
            
            if ($db->query($sql, $params)) {
                echo json_encode(['success' => true, 'message' => 'Konu başarıyla güncellendi']);
            } else {
                echo json_encode(['success' => false, 'message' => 'Konu güncellenirken hata oluştu']);
            }
            break;

        case 'DELETE':
            // Delete subject (soft delete)
            $input = json_decode(file_get_contents("php://input"), true);
            $id = (int)($input['id'] ?? 0);
            
            if (!$id) {
                echo json_encode(['success' => false, 'message' => 'ID gerekli']);
                exit;
            }
            
            // Check if subject is used in contacts
            $used = $db->fetch("SELECT COUNT(*) as count FROM contacts WHERE subject_id = :id", ['id' => $id]);
            
            if ($used['count'] > 0) {
                echo json_encode(['success' => false, 'message' => 'Bu konu iletişim taleplerinde kullanılıyor, silinemez']);
                exit;
            }
            
            $sql = "UPDATE contact_subjects SET status = 'inactive', updated_at = CURRENT_TIMESTAMP WHERE id = :id";
            
            if ($db->query($sql, ['id' => $id])) {
                echo json_encode(['success' => true, 'message' => 'Konu başarıyla silindi']);
            } else {
                echo json_encode(['success' => false, 'message' => 'Konu silinirken hata oluştu']);
            }
            break;

        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'error' => 'Method not allowed']);
            break;
    }

} catch (Exception $e) {
    error_log("Admin Contact subjects API error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Veritabanı hatası']);
}
