"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/tabbable";
exports.ids = ["vendor-chunks/tabbable"];
exports.modules = {

/***/ "(ssr)/./node_modules/tabbable/dist/index.esm.js":
/*!*************************************************!*\
  !*** ./node_modules/tabbable/dist/index.esm.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   focusable: () => (/* binding */ focusable),\n/* harmony export */   getTabIndex: () => (/* binding */ getTabIndex),\n/* harmony export */   isFocusable: () => (/* binding */ isFocusable),\n/* harmony export */   isTabbable: () => (/* binding */ isTabbable),\n/* harmony export */   tabbable: () => (/* binding */ tabbable)\n/* harmony export */ });\n/*!\n* tabbable 6.2.0\n* @license MIT, https://github.com/focus-trap/tabbable/blob/master/LICENSE\n*/\n// NOTE: separate `:not()` selectors has broader browser support than the newer\n//  `:not([inert], [inert] *)` (Feb 2023)\n// CAREFUL: JSDom does not support `:not([inert] *)` as a selector; using it causes\n//  the entire query to fail, resulting in no nodes found, which will break a lot\n//  of things... so we have to rely on JS to identify nodes inside an inert container\nvar candidateSelectors = ['input:not([inert])', 'select:not([inert])', 'textarea:not([inert])', 'a[href]:not([inert])', 'button:not([inert])', '[tabindex]:not(slot):not([inert])', 'audio[controls]:not([inert])', 'video[controls]:not([inert])', '[contenteditable]:not([contenteditable=\"false\"]):not([inert])', 'details>summary:first-of-type:not([inert])', 'details:not([inert])'];\nvar candidateSelector = /* #__PURE__ */candidateSelectors.join(',');\nvar NoElement = typeof Element === 'undefined';\nvar matches = NoElement ? function () {} : Element.prototype.matches || Element.prototype.msMatchesSelector || Element.prototype.webkitMatchesSelector;\nvar getRootNode = !NoElement && Element.prototype.getRootNode ? function (element) {\n  var _element$getRootNode;\n  return element === null || element === void 0 ? void 0 : (_element$getRootNode = element.getRootNode) === null || _element$getRootNode === void 0 ? void 0 : _element$getRootNode.call(element);\n} : function (element) {\n  return element === null || element === void 0 ? void 0 : element.ownerDocument;\n};\n\n/**\n * Determines if a node is inert or in an inert ancestor.\n * @param {Element} [node]\n * @param {boolean} [lookUp] If true and `node` is not inert, looks up at ancestors to\n *  see if any of them are inert. If false, only `node` itself is considered.\n * @returns {boolean} True if inert itself or by way of being in an inert ancestor.\n *  False if `node` is falsy.\n */\nvar isInert = function isInert(node, lookUp) {\n  var _node$getAttribute;\n  if (lookUp === void 0) {\n    lookUp = true;\n  }\n  // CAREFUL: JSDom does not support inert at all, so we can't use the `HTMLElement.inert`\n  //  JS API property; we have to check the attribute, which can either be empty or 'true';\n  //  if it's `null` (not specified) or 'false', it's an active element\n  var inertAtt = node === null || node === void 0 ? void 0 : (_node$getAttribute = node.getAttribute) === null || _node$getAttribute === void 0 ? void 0 : _node$getAttribute.call(node, 'inert');\n  var inert = inertAtt === '' || inertAtt === 'true';\n\n  // NOTE: this could also be handled with `node.matches('[inert], :is([inert] *)')`\n  //  if it weren't for `matches()` not being a function on shadow roots; the following\n  //  code works for any kind of node\n  // CAREFUL: JSDom does not appear to support certain selectors like `:not([inert] *)`\n  //  so it likely would not support `:is([inert] *)` either...\n  var result = inert || lookUp && node && isInert(node.parentNode); // recursive\n\n  return result;\n};\n\n/**\n * Determines if a node's content is editable.\n * @param {Element} [node]\n * @returns True if it's content-editable; false if it's not or `node` is falsy.\n */\nvar isContentEditable = function isContentEditable(node) {\n  var _node$getAttribute2;\n  // CAREFUL: JSDom does not support the `HTMLElement.isContentEditable` API so we have\n  //  to use the attribute directly to check for this, which can either be empty or 'true';\n  //  if it's `null` (not specified) or 'false', it's a non-editable element\n  var attValue = node === null || node === void 0 ? void 0 : (_node$getAttribute2 = node.getAttribute) === null || _node$getAttribute2 === void 0 ? void 0 : _node$getAttribute2.call(node, 'contenteditable');\n  return attValue === '' || attValue === 'true';\n};\n\n/**\n * @param {Element} el container to check in\n * @param {boolean} includeContainer add container to check\n * @param {(node: Element) => boolean} filter filter candidates\n * @returns {Element[]}\n */\nvar getCandidates = function getCandidates(el, includeContainer, filter) {\n  // even if `includeContainer=false`, we still have to check it for inertness because\n  //  if it's inert, all its children are inert\n  if (isInert(el)) {\n    return [];\n  }\n  var candidates = Array.prototype.slice.apply(el.querySelectorAll(candidateSelector));\n  if (includeContainer && matches.call(el, candidateSelector)) {\n    candidates.unshift(el);\n  }\n  candidates = candidates.filter(filter);\n  return candidates;\n};\n\n/**\n * @callback GetShadowRoot\n * @param {Element} element to check for shadow root\n * @returns {ShadowRoot|boolean} ShadowRoot if available or boolean indicating if a shadowRoot is attached but not available.\n */\n\n/**\n * @callback ShadowRootFilter\n * @param {Element} shadowHostNode the element which contains shadow content\n * @returns {boolean} true if a shadow root could potentially contain valid candidates.\n */\n\n/**\n * @typedef {Object} CandidateScope\n * @property {Element} scopeParent contains inner candidates\n * @property {Element[]} candidates list of candidates found in the scope parent\n */\n\n/**\n * @typedef {Object} IterativeOptions\n * @property {GetShadowRoot|boolean} getShadowRoot true if shadow support is enabled; falsy if not;\n *  if a function, implies shadow support is enabled and either returns the shadow root of an element\n *  or a boolean stating if it has an undisclosed shadow root\n * @property {(node: Element) => boolean} filter filter candidates\n * @property {boolean} flatten if true then result will flatten any CandidateScope into the returned list\n * @property {ShadowRootFilter} shadowRootFilter filter shadow roots;\n */\n\n/**\n * @param {Element[]} elements list of element containers to match candidates from\n * @param {boolean} includeContainer add container list to check\n * @param {IterativeOptions} options\n * @returns {Array.<Element|CandidateScope>}\n */\nvar getCandidatesIteratively = function getCandidatesIteratively(elements, includeContainer, options) {\n  var candidates = [];\n  var elementsToCheck = Array.from(elements);\n  while (elementsToCheck.length) {\n    var element = elementsToCheck.shift();\n    if (isInert(element, false)) {\n      // no need to look up since we're drilling down\n      // anything inside this container will also be inert\n      continue;\n    }\n    if (element.tagName === 'SLOT') {\n      // add shadow dom slot scope (slot itself cannot be focusable)\n      var assigned = element.assignedElements();\n      var content = assigned.length ? assigned : element.children;\n      var nestedCandidates = getCandidatesIteratively(content, true, options);\n      if (options.flatten) {\n        candidates.push.apply(candidates, nestedCandidates);\n      } else {\n        candidates.push({\n          scopeParent: element,\n          candidates: nestedCandidates\n        });\n      }\n    } else {\n      // check candidate element\n      var validCandidate = matches.call(element, candidateSelector);\n      if (validCandidate && options.filter(element) && (includeContainer || !elements.includes(element))) {\n        candidates.push(element);\n      }\n\n      // iterate over shadow content if possible\n      var shadowRoot = element.shadowRoot ||\n      // check for an undisclosed shadow\n      typeof options.getShadowRoot === 'function' && options.getShadowRoot(element);\n\n      // no inert look up because we're already drilling down and checking for inertness\n      //  on the way down, so all containers to this root node should have already been\n      //  vetted as non-inert\n      var validShadowRoot = !isInert(shadowRoot, false) && (!options.shadowRootFilter || options.shadowRootFilter(element));\n      if (shadowRoot && validShadowRoot) {\n        // add shadow dom scope IIF a shadow root node was given; otherwise, an undisclosed\n        //  shadow exists, so look at light dom children as fallback BUT create a scope for any\n        //  child candidates found because they're likely slotted elements (elements that are\n        //  children of the web component element (which has the shadow), in the light dom, but\n        //  slotted somewhere _inside_ the undisclosed shadow) -- the scope is created below,\n        //  _after_ we return from this recursive call\n        var _nestedCandidates = getCandidatesIteratively(shadowRoot === true ? element.children : shadowRoot.children, true, options);\n        if (options.flatten) {\n          candidates.push.apply(candidates, _nestedCandidates);\n        } else {\n          candidates.push({\n            scopeParent: element,\n            candidates: _nestedCandidates\n          });\n        }\n      } else {\n        // there's not shadow so just dig into the element's (light dom) children\n        //  __without__ giving the element special scope treatment\n        elementsToCheck.unshift.apply(elementsToCheck, element.children);\n      }\n    }\n  }\n  return candidates;\n};\n\n/**\n * @private\n * Determines if the node has an explicitly specified `tabindex` attribute.\n * @param {HTMLElement} node\n * @returns {boolean} True if so; false if not.\n */\nvar hasTabIndex = function hasTabIndex(node) {\n  return !isNaN(parseInt(node.getAttribute('tabindex'), 10));\n};\n\n/**\n * Determine the tab index of a given node.\n * @param {HTMLElement} node\n * @returns {number} Tab order (negative, 0, or positive number).\n * @throws {Error} If `node` is falsy.\n */\nvar getTabIndex = function getTabIndex(node) {\n  if (!node) {\n    throw new Error('No node provided');\n  }\n  if (node.tabIndex < 0) {\n    // in Chrome, <details/>, <audio controls/> and <video controls/> elements get a default\n    // `tabIndex` of -1 when the 'tabindex' attribute isn't specified in the DOM,\n    // yet they are still part of the regular tab order; in FF, they get a default\n    // `tabIndex` of 0; since Chrome still puts those elements in the regular tab\n    // order, consider their tab index to be 0.\n    // Also browsers do not return `tabIndex` correctly for contentEditable nodes;\n    // so if they don't have a tabindex attribute specifically set, assume it's 0.\n    if ((/^(AUDIO|VIDEO|DETAILS)$/.test(node.tagName) || isContentEditable(node)) && !hasTabIndex(node)) {\n      return 0;\n    }\n  }\n  return node.tabIndex;\n};\n\n/**\n * Determine the tab index of a given node __for sort order purposes__.\n * @param {HTMLElement} node\n * @param {boolean} [isScope] True for a custom element with shadow root or slot that, by default,\n *  has tabIndex -1, but needs to be sorted by document order in order for its content to be\n *  inserted into the correct sort position.\n * @returns {number} Tab order (negative, 0, or positive number).\n */\nvar getSortOrderTabIndex = function getSortOrderTabIndex(node, isScope) {\n  var tabIndex = getTabIndex(node);\n  if (tabIndex < 0 && isScope && !hasTabIndex(node)) {\n    return 0;\n  }\n  return tabIndex;\n};\nvar sortOrderedTabbables = function sortOrderedTabbables(a, b) {\n  return a.tabIndex === b.tabIndex ? a.documentOrder - b.documentOrder : a.tabIndex - b.tabIndex;\n};\nvar isInput = function isInput(node) {\n  return node.tagName === 'INPUT';\n};\nvar isHiddenInput = function isHiddenInput(node) {\n  return isInput(node) && node.type === 'hidden';\n};\nvar isDetailsWithSummary = function isDetailsWithSummary(node) {\n  var r = node.tagName === 'DETAILS' && Array.prototype.slice.apply(node.children).some(function (child) {\n    return child.tagName === 'SUMMARY';\n  });\n  return r;\n};\nvar getCheckedRadio = function getCheckedRadio(nodes, form) {\n  for (var i = 0; i < nodes.length; i++) {\n    if (nodes[i].checked && nodes[i].form === form) {\n      return nodes[i];\n    }\n  }\n};\nvar isTabbableRadio = function isTabbableRadio(node) {\n  if (!node.name) {\n    return true;\n  }\n  var radioScope = node.form || getRootNode(node);\n  var queryRadios = function queryRadios(name) {\n    return radioScope.querySelectorAll('input[type=\"radio\"][name=\"' + name + '\"]');\n  };\n  var radioSet;\n  if (typeof window !== 'undefined' && typeof window.CSS !== 'undefined' && typeof window.CSS.escape === 'function') {\n    radioSet = queryRadios(window.CSS.escape(node.name));\n  } else {\n    try {\n      radioSet = queryRadios(node.name);\n    } catch (err) {\n      // eslint-disable-next-line no-console\n      console.error('Looks like you have a radio button with a name attribute containing invalid CSS selector characters and need the CSS.escape polyfill: %s', err.message);\n      return false;\n    }\n  }\n  var checked = getCheckedRadio(radioSet, node.form);\n  return !checked || checked === node;\n};\nvar isRadio = function isRadio(node) {\n  return isInput(node) && node.type === 'radio';\n};\nvar isNonTabbableRadio = function isNonTabbableRadio(node) {\n  return isRadio(node) && !isTabbableRadio(node);\n};\n\n// determines if a node is ultimately attached to the window's document\nvar isNodeAttached = function isNodeAttached(node) {\n  var _nodeRoot;\n  // The root node is the shadow root if the node is in a shadow DOM; some document otherwise\n  //  (but NOT _the_ document; see second 'If' comment below for more).\n  // If rootNode is shadow root, it'll have a host, which is the element to which the shadow\n  //  is attached, and the one we need to check if it's in the document or not (because the\n  //  shadow, and all nodes it contains, is never considered in the document since shadows\n  //  behave like self-contained DOMs; but if the shadow's HOST, which is part of the document,\n  //  is hidden, or is not in the document itself but is detached, it will affect the shadow's\n  //  visibility, including all the nodes it contains). The host could be any normal node,\n  //  or a custom element (i.e. web component). Either way, that's the one that is considered\n  //  part of the document, not the shadow root, nor any of its children (i.e. the node being\n  //  tested).\n  // To further complicate things, we have to look all the way up until we find a shadow HOST\n  //  that is attached (or find none) because the node might be in nested shadows...\n  // If rootNode is not a shadow root, it won't have a host, and so rootNode should be the\n  //  document (per the docs) and while it's a Document-type object, that document does not\n  //  appear to be the same as the node's `ownerDocument` for some reason, so it's safer\n  //  to ignore the rootNode at this point, and use `node.ownerDocument`. Otherwise,\n  //  using `rootNode.contains(node)` will _always_ be true we'll get false-positives when\n  //  node is actually detached.\n  // NOTE: If `nodeRootHost` or `node` happens to be the `document` itself (which is possible\n  //  if a tabbable/focusable node was quickly added to the DOM, focused, and then removed\n  //  from the DOM as in https://github.com/focus-trap/focus-trap-react/issues/905), then\n  //  `ownerDocument` will be `null`, hence the optional chaining on it.\n  var nodeRoot = node && getRootNode(node);\n  var nodeRootHost = (_nodeRoot = nodeRoot) === null || _nodeRoot === void 0 ? void 0 : _nodeRoot.host;\n\n  // in some cases, a detached node will return itself as the root instead of a document or\n  //  shadow root object, in which case, we shouldn't try to look further up the host chain\n  var attached = false;\n  if (nodeRoot && nodeRoot !== node) {\n    var _nodeRootHost, _nodeRootHost$ownerDo, _node$ownerDocument;\n    attached = !!((_nodeRootHost = nodeRootHost) !== null && _nodeRootHost !== void 0 && (_nodeRootHost$ownerDo = _nodeRootHost.ownerDocument) !== null && _nodeRootHost$ownerDo !== void 0 && _nodeRootHost$ownerDo.contains(nodeRootHost) || node !== null && node !== void 0 && (_node$ownerDocument = node.ownerDocument) !== null && _node$ownerDocument !== void 0 && _node$ownerDocument.contains(node));\n    while (!attached && nodeRootHost) {\n      var _nodeRoot2, _nodeRootHost2, _nodeRootHost2$ownerD;\n      // since it's not attached and we have a root host, the node MUST be in a nested shadow DOM,\n      //  which means we need to get the host's host and check if that parent host is contained\n      //  in (i.e. attached to) the document\n      nodeRoot = getRootNode(nodeRootHost);\n      nodeRootHost = (_nodeRoot2 = nodeRoot) === null || _nodeRoot2 === void 0 ? void 0 : _nodeRoot2.host;\n      attached = !!((_nodeRootHost2 = nodeRootHost) !== null && _nodeRootHost2 !== void 0 && (_nodeRootHost2$ownerD = _nodeRootHost2.ownerDocument) !== null && _nodeRootHost2$ownerD !== void 0 && _nodeRootHost2$ownerD.contains(nodeRootHost));\n    }\n  }\n  return attached;\n};\nvar isZeroArea = function isZeroArea(node) {\n  var _node$getBoundingClie = node.getBoundingClientRect(),\n    width = _node$getBoundingClie.width,\n    height = _node$getBoundingClie.height;\n  return width === 0 && height === 0;\n};\nvar isHidden = function isHidden(node, _ref) {\n  var displayCheck = _ref.displayCheck,\n    getShadowRoot = _ref.getShadowRoot;\n  // NOTE: visibility will be `undefined` if node is detached from the document\n  //  (see notes about this further down), which means we will consider it visible\n  //  (this is legacy behavior from a very long way back)\n  // NOTE: we check this regardless of `displayCheck=\"none\"` because this is a\n  //  _visibility_ check, not a _display_ check\n  if (getComputedStyle(node).visibility === 'hidden') {\n    return true;\n  }\n  var isDirectSummary = matches.call(node, 'details>summary:first-of-type');\n  var nodeUnderDetails = isDirectSummary ? node.parentElement : node;\n  if (matches.call(nodeUnderDetails, 'details:not([open]) *')) {\n    return true;\n  }\n  if (!displayCheck || displayCheck === 'full' || displayCheck === 'legacy-full') {\n    if (typeof getShadowRoot === 'function') {\n      // figure out if we should consider the node to be in an undisclosed shadow and use the\n      //  'non-zero-area' fallback\n      var originalNode = node;\n      while (node) {\n        var parentElement = node.parentElement;\n        var rootNode = getRootNode(node);\n        if (parentElement && !parentElement.shadowRoot && getShadowRoot(parentElement) === true // check if there's an undisclosed shadow\n        ) {\n          // node has an undisclosed shadow which means we can only treat it as a black box, so we\n          //  fall back to a non-zero-area test\n          return isZeroArea(node);\n        } else if (node.assignedSlot) {\n          // iterate up slot\n          node = node.assignedSlot;\n        } else if (!parentElement && rootNode !== node.ownerDocument) {\n          // cross shadow boundary\n          node = rootNode.host;\n        } else {\n          // iterate up normal dom\n          node = parentElement;\n        }\n      }\n      node = originalNode;\n    }\n    // else, `getShadowRoot` might be true, but all that does is enable shadow DOM support\n    //  (i.e. it does not also presume that all nodes might have undisclosed shadows); or\n    //  it might be a falsy value, which means shadow DOM support is disabled\n\n    // Since we didn't find it sitting in an undisclosed shadow (or shadows are disabled)\n    //  now we can just test to see if it would normally be visible or not, provided it's\n    //  attached to the main document.\n    // NOTE: We must consider case where node is inside a shadow DOM and given directly to\n    //  `isTabbable()` or `isFocusable()` -- regardless of `getShadowRoot` option setting.\n\n    if (isNodeAttached(node)) {\n      // this works wherever the node is: if there's at least one client rect, it's\n      //  somehow displayed; it also covers the CSS 'display: contents' case where the\n      //  node itself is hidden in place of its contents; and there's no need to search\n      //  up the hierarchy either\n      return !node.getClientRects().length;\n    }\n\n    // Else, the node isn't attached to the document, which means the `getClientRects()`\n    //  API will __always__ return zero rects (this can happen, for example, if React\n    //  is used to render nodes onto a detached tree, as confirmed in this thread:\n    //  https://github.com/facebook/react/issues/9117#issuecomment-284228870)\n    //\n    // It also means that even window.getComputedStyle(node).display will return `undefined`\n    //  because styles are only computed for nodes that are in the document.\n    //\n    // NOTE: THIS HAS BEEN THE CASE FOR YEARS. It is not new, nor is it caused by tabbable\n    //  somehow. Though it was never stated officially, anyone who has ever used tabbable\n    //  APIs on nodes in detached containers has actually implicitly used tabbable in what\n    //  was later (as of v5.2.0 on Apr 9, 2021) called `displayCheck=\"none\"` mode -- essentially\n    //  considering __everything__ to be visible because of the innability to determine styles.\n    //\n    // v6.0.0: As of this major release, the default 'full' option __no longer treats detached\n    //  nodes as visible with the 'none' fallback.__\n    if (displayCheck !== 'legacy-full') {\n      return true; // hidden\n    }\n    // else, fallback to 'none' mode and consider the node visible\n  } else if (displayCheck === 'non-zero-area') {\n    // NOTE: Even though this tests that the node's client rect is non-zero to determine\n    //  whether it's displayed, and that a detached node will __always__ have a zero-area\n    //  client rect, we don't special-case for whether the node is attached or not. In\n    //  this mode, we do want to consider nodes that have a zero area to be hidden at all\n    //  times, and that includes attached or not.\n    return isZeroArea(node);\n  }\n\n  // visible, as far as we can tell, or per current `displayCheck=none` mode, we assume\n  //  it's visible\n  return false;\n};\n\n// form fields (nested) inside a disabled fieldset are not focusable/tabbable\n//  unless they are in the _first_ <legend> element of the top-most disabled\n//  fieldset\nvar isDisabledFromFieldset = function isDisabledFromFieldset(node) {\n  if (/^(INPUT|BUTTON|SELECT|TEXTAREA)$/.test(node.tagName)) {\n    var parentNode = node.parentElement;\n    // check if `node` is contained in a disabled <fieldset>\n    while (parentNode) {\n      if (parentNode.tagName === 'FIELDSET' && parentNode.disabled) {\n        // look for the first <legend> among the children of the disabled <fieldset>\n        for (var i = 0; i < parentNode.children.length; i++) {\n          var child = parentNode.children.item(i);\n          // when the first <legend> (in document order) is found\n          if (child.tagName === 'LEGEND') {\n            // if its parent <fieldset> is not nested in another disabled <fieldset>,\n            // return whether `node` is a descendant of its first <legend>\n            return matches.call(parentNode, 'fieldset[disabled] *') ? true : !child.contains(node);\n          }\n        }\n        // the disabled <fieldset> containing `node` has no <legend>\n        return true;\n      }\n      parentNode = parentNode.parentElement;\n    }\n  }\n\n  // else, node's tabbable/focusable state should not be affected by a fieldset's\n  //  enabled/disabled state\n  return false;\n};\nvar isNodeMatchingSelectorFocusable = function isNodeMatchingSelectorFocusable(options, node) {\n  if (node.disabled ||\n  // we must do an inert look up to filter out any elements inside an inert ancestor\n  //  because we're limited in the type of selectors we can use in JSDom (see related\n  //  note related to `candidateSelectors`)\n  isInert(node) || isHiddenInput(node) || isHidden(node, options) ||\n  // For a details element with a summary, the summary element gets the focus\n  isDetailsWithSummary(node) || isDisabledFromFieldset(node)) {\n    return false;\n  }\n  return true;\n};\nvar isNodeMatchingSelectorTabbable = function isNodeMatchingSelectorTabbable(options, node) {\n  if (isNonTabbableRadio(node) || getTabIndex(node) < 0 || !isNodeMatchingSelectorFocusable(options, node)) {\n    return false;\n  }\n  return true;\n};\nvar isValidShadowRootTabbable = function isValidShadowRootTabbable(shadowHostNode) {\n  var tabIndex = parseInt(shadowHostNode.getAttribute('tabindex'), 10);\n  if (isNaN(tabIndex) || tabIndex >= 0) {\n    return true;\n  }\n  // If a custom element has an explicit negative tabindex,\n  // browsers will not allow tab targeting said element's children.\n  return false;\n};\n\n/**\n * @param {Array.<Element|CandidateScope>} candidates\n * @returns Element[]\n */\nvar sortByOrder = function sortByOrder(candidates) {\n  var regularTabbables = [];\n  var orderedTabbables = [];\n  candidates.forEach(function (item, i) {\n    var isScope = !!item.scopeParent;\n    var element = isScope ? item.scopeParent : item;\n    var candidateTabindex = getSortOrderTabIndex(element, isScope);\n    var elements = isScope ? sortByOrder(item.candidates) : element;\n    if (candidateTabindex === 0) {\n      isScope ? regularTabbables.push.apply(regularTabbables, elements) : regularTabbables.push(element);\n    } else {\n      orderedTabbables.push({\n        documentOrder: i,\n        tabIndex: candidateTabindex,\n        item: item,\n        isScope: isScope,\n        content: elements\n      });\n    }\n  });\n  return orderedTabbables.sort(sortOrderedTabbables).reduce(function (acc, sortable) {\n    sortable.isScope ? acc.push.apply(acc, sortable.content) : acc.push(sortable.content);\n    return acc;\n  }, []).concat(regularTabbables);\n};\nvar tabbable = function tabbable(container, options) {\n  options = options || {};\n  var candidates;\n  if (options.getShadowRoot) {\n    candidates = getCandidatesIteratively([container], options.includeContainer, {\n      filter: isNodeMatchingSelectorTabbable.bind(null, options),\n      flatten: false,\n      getShadowRoot: options.getShadowRoot,\n      shadowRootFilter: isValidShadowRootTabbable\n    });\n  } else {\n    candidates = getCandidates(container, options.includeContainer, isNodeMatchingSelectorTabbable.bind(null, options));\n  }\n  return sortByOrder(candidates);\n};\nvar focusable = function focusable(container, options) {\n  options = options || {};\n  var candidates;\n  if (options.getShadowRoot) {\n    candidates = getCandidatesIteratively([container], options.includeContainer, {\n      filter: isNodeMatchingSelectorFocusable.bind(null, options),\n      flatten: true,\n      getShadowRoot: options.getShadowRoot\n    });\n  } else {\n    candidates = getCandidates(container, options.includeContainer, isNodeMatchingSelectorFocusable.bind(null, options));\n  }\n  return candidates;\n};\nvar isTabbable = function isTabbable(node, options) {\n  options = options || {};\n  if (!node) {\n    throw new Error('No node provided');\n  }\n  if (matches.call(node, candidateSelector) === false) {\n    return false;\n  }\n  return isNodeMatchingSelectorTabbable(options, node);\n};\nvar focusableCandidateSelector = /* #__PURE__ */candidateSelectors.concat('iframe').join(',');\nvar isFocusable = function isFocusable(node, options) {\n  options = options || {};\n  if (!node) {\n    throw new Error('No node provided');\n  }\n  if (matches.call(node, focusableCandidateSelector) === false) {\n    return false;\n  }\n  return isNodeMatchingSelectorFocusable(options, node);\n};\n\n\n//# sourceMappingURL=index.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tabbable/dist/index.esm.js\n");

/***/ })

};
;