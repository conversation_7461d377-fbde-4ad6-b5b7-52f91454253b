'use client';

import { useState, useEffect } from 'react';
import WithAuth from '@/components/admin/auth/WithAuth';
import AdminLayout from '@/components/admin/layout/AdminLayout';
import { fetchFromAdminApi } from '@/utils/api';
import toast from 'react-hot-toast';
import { Button } from '@/components/ui/Button';
import { Modal } from '@/components/ui/Modal';
import { 
  PhotoIcon, 
  PlusIcon, 
  PencilIcon, 
  TrashIcon,
  ArrowUpIcon,
  ArrowDownIcon 
} from '@heroicons/react/24/outline';

interface HeroSlide {
  id: number;
  title_tr: string;
  title_en: string;
  subtitle_tr: string;
  subtitle_en: string;
  image_path: string;
  sort_order: number;
  status: string;
}

interface Service {
  id: number;
  title_tr: string;
  title_en: string;
  description_tr: string;
  description_en: string;
  icon: string;
  featured: boolean;
  sort_order: number;
  status: string;
}

export default function HomepagePage() {
  return (
    <WithAuth>
      <HomepageContent />
    </WithAuth>
  );
}

function HomepageContent() {
  const [activeTab, setActiveTab] = useState<'hero' | 'services'>('hero');
  const [heroSlides, setHeroSlides] = useState<HeroSlide[]>([]);
  const [services, setServices] = useState<Service[]>([]);
  const [loading, setLoading] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingItem, setEditingItem] = useState<HeroSlide | Service | null>(null);

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      const [heroResponse, servicesResponse] = await Promise.all([
        fetchFromAdminApi('/hero-slides.php'),
        fetchFromAdminApi('/services.php')
      ]);

      if (heroResponse.success && heroResponse.data) {
        setHeroSlides(heroResponse.data);
      }

      if (servicesResponse.success && servicesResponse.data) {
        setServices(servicesResponse.data);
      }
    } catch (error) {
      console.error('Veri yüklenirken hata:', error);
      toast.error('Veriler yüklenirken hata oluştu');
    } finally {
      setLoading(false);
    }
  };

  const handleAddNew = () => {
    if (activeTab === 'hero') {
      setEditingItem({
        id: 0,
        title_tr: '',
        title_en: '',
        subtitle_tr: '',
        subtitle_en: '',
        image_path: '',
        sort_order: heroSlides.length + 1,
        status: 'active'
      } as HeroSlide);
    } else {
      setEditingItem({
        id: 0,
        title_tr: '',
        title_en: '',
        description_tr: '',
        description_en: '',
        icon: 'atom',
        featured: false,
        sort_order: services.length + 1,
        status: 'active'
      } as Service);
    }
    setIsModalOpen(true);
  };

  const handleEdit = (item: HeroSlide | Service) => {
    setEditingItem(item);
    setIsModalOpen(true);
  };

  const handleDelete = async (id: number) => {
    if (!confirm('Bu öğeyi silmek istediğinizden emin misiniz?')) return;

    try {
      const endpoint = activeTab === 'hero' ? '/hero-slides.php' : '/services.php';
      const response = await fetchFromAdminApi(endpoint, {
        method: 'DELETE',
        body: JSON.stringify({ id })
      });

      if (response.success) {
        toast.success('Öğe başarıyla silindi');
        fetchData();
      } else {
        toast.error(response.message || 'Silme işlemi başarısız');
      }
    } catch (error) {
      console.error('Silme hatası:', error);
      toast.error('Silme işlemi sırasında hata oluştu');
    }
  };

  const handleSave = async (formData: any) => {
    try {
      const endpoint = activeTab === 'hero' ? '/hero-slides.php' : '/services.php';
      const method = editingItem?.id ? 'PUT' : 'POST';
      
      const response = await fetchFromAdminApi(endpoint, {
        method,
        body: JSON.stringify(formData)
      });

      if (response.success) {
        toast.success(editingItem?.id ? 'Güncelleme başarılı' : 'Ekleme başarılı');
        setIsModalOpen(false);
        setEditingItem(null);
        fetchData();
      } else {
        toast.error(response.message || 'İşlem başarısız');
      }
    } catch (error) {
      console.error('Kaydetme hatası:', error);
      toast.error('Kaydetme sırasında hata oluştu');
    }
  };

  const moveItem = async (id: number, direction: 'up' | 'down') => {
    try {
      const endpoint = activeTab === 'hero' ? '/hero-slides.php' : '/services.php';
      const response = await fetchFromAdminApi(`${endpoint}?action=reorder`, {
        method: 'POST',
        body: JSON.stringify({ id, direction })
      });

      if (response.success) {
        fetchData();
      } else {
        toast.error('Sıralama değiştirilemedi');
      }
    } catch (error) {
      console.error('Sıralama hatası:', error);
      toast.error('Sıralama değiştirilirken hata oluştu');
    }
  };

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="px-4 sm:px-6 lg:px-8 py-8">
        <div className="sm:flex sm:items-center">
          <div className="sm:flex-auto">
            <h1 className="text-xl font-semibold text-secondary-900">Ana Sayfa Yönetimi</h1>
            <p className="mt-2 text-sm text-secondary-700">
              Ana sayfadaki slider ve hizmetler bölümünü buradan yönetebilirsiniz.
            </p>
          </div>
          <div className="mt-4 sm:ml-16 sm:mt-0 sm:flex-none">
            <Button
              onClick={handleAddNew}
              className="bg-primary-600 hover:bg-primary-500"
            >
              <PlusIcon className="h-4 w-4 mr-2" />
              Yeni Ekle
            </Button>
          </div>
        </div>

        {/* Tabs */}
        <div className="mt-8">
          <div className="border-b border-secondary-200">
            <nav className="-mb-px flex space-x-8">
              <button
                onClick={() => setActiveTab('hero')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'hero'
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-secondary-500 hover:text-secondary-700 hover:border-secondary-300'
                }`}
              >
                Hero Slider
              </button>
              <button
                onClick={() => setActiveTab('services')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'services'
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-secondary-500 hover:text-secondary-700 hover:border-secondary-300'
                }`}
              >
                Hizmetler
              </button>
            </nav>
          </div>
        </div>

        {/* Content */}
        <div className="mt-8">
          {activeTab === 'hero' ? (
            <HeroSlidesTable 
              slides={heroSlides} 
              onEdit={handleEdit}
              onDelete={handleDelete}
              onMove={moveItem}
            />
          ) : (
            <ServicesTable 
              services={services} 
              onEdit={handleEdit}
              onDelete={handleDelete}
              onMove={moveItem}
            />
          )}
        </div>

        {/* Modal */}
        {isModalOpen && editingItem && (
          <EditModal
            item={editingItem}
            type={activeTab}
            onSave={handleSave}
            onClose={() => {
              setIsModalOpen(false);
              setEditingItem(null);
            }}
          />
        )}
      </div>
    </AdminLayout>
  );
}

// Hero Slides Table Component
function HeroSlidesTable({ 
  slides, 
  onEdit, 
  onDelete, 
  onMove 
}: { 
  slides: HeroSlide[];
  onEdit: (slide: HeroSlide) => void;
  onDelete: (id: number) => void;
  onMove: (id: number, direction: 'up' | 'down') => void;
}) {
  return (
    <div className="bg-white shadow rounded-lg overflow-hidden">
      <table className="min-w-full divide-y divide-secondary-200">
        <thead className="bg-secondary-50">
          <tr>
            <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
              Görsel
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
              Başlık
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
              Durum
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
              Sıra
            </th>
            <th className="px-6 py-3 text-right text-xs font-medium text-secondary-500 uppercase tracking-wider">
              İşlemler
            </th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-secondary-200">
          {slides.map((slide, index) => (
            <tr key={slide.id}>
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="h-16 w-24 bg-secondary-100 rounded-lg overflow-hidden">
                  {slide.image_path ? (
                    <img
                      src={slide.image_path}
                      alt={slide.title_tr}
                      className="h-full w-full object-cover"
                    />
                  ) : (
                    <div className="h-full w-full flex items-center justify-center">
                      <PhotoIcon className="h-8 w-8 text-secondary-400" />
                    </div>
                  )}
                </div>
              </td>
              <td className="px-6 py-4">
                <div className="text-sm font-medium text-secondary-900">
                  {slide.title_tr}
                </div>
                <div className="text-sm text-secondary-500 truncate max-w-xs">
                  {slide.subtitle_tr}
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                  slide.status === 'active' 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-red-100 text-red-800'
                }`}>
                  {slide.status === 'active' ? 'Aktif' : 'Pasif'}
                </span>
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-secondary-500">
                <div className="flex items-center space-x-2">
                  <span>{slide.sort_order}</span>
                  <div className="flex flex-col space-y-1">
                    <button
                      onClick={() => onMove(slide.id, 'up')}
                      disabled={index === 0}
                      className="p-1 text-secondary-400 hover:text-secondary-600 disabled:opacity-50"
                    >
                      <ArrowUpIcon className="h-3 w-3" />
                    </button>
                    <button
                      onClick={() => onMove(slide.id, 'down')}
                      disabled={index === slides.length - 1}
                      className="p-1 text-secondary-400 hover:text-secondary-600 disabled:opacity-50"
                    >
                      <ArrowDownIcon className="h-3 w-3" />
                    </button>
                  </div>
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <div className="flex items-center justify-end space-x-2">
                  <button
                    onClick={() => onEdit(slide)}
                    className="text-primary-600 hover:text-primary-900"
                  >
                    <PencilIcon className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => onDelete(slide.id)}
                    className="text-red-600 hover:text-red-900"
                  >
                    <TrashIcon className="h-4 w-4" />
                  </button>
                </div>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}

// Services Table Component
function ServicesTable({
  services,
  onEdit,
  onDelete,
  onMove
}: {
  services: Service[];
  onEdit: (service: Service) => void;
  onDelete: (id: number) => void;
  onMove: (id: number, direction: 'up' | 'down') => void;
}) {
  return (
    <div className="bg-white shadow rounded-lg overflow-hidden">
      <table className="min-w-full divide-y divide-secondary-200">
        <thead className="bg-secondary-50">
          <tr>
            <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
              İkon
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
              Başlık
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
              Öne Çıkan
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
              Durum
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
              Sıra
            </th>
            <th className="px-6 py-3 text-right text-xs font-medium text-secondary-500 uppercase tracking-wider">
              İşlemler
            </th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-secondary-200">
          {services.map((service, index) => (
            <tr key={service.id}>
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="h-10 w-10 bg-primary-100 rounded-lg flex items-center justify-center">
                  <span className="text-primary-600 font-semibold text-sm">
                    {service.icon.charAt(0).toUpperCase()}
                  </span>
                </div>
              </td>
              <td className="px-6 py-4">
                <div className="text-sm font-medium text-secondary-900">
                  {service.title_tr}
                </div>
                <div className="text-sm text-secondary-500 truncate max-w-xs">
                  {service.description_tr}
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                  service.featured
                    ? 'bg-yellow-100 text-yellow-800'
                    : 'bg-secondary-100 text-secondary-800'
                }`}>
                  {service.featured ? 'Evet' : 'Hayır'}
                </span>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                  service.status === 'active'
                    ? 'bg-green-100 text-green-800'
                    : 'bg-red-100 text-red-800'
                }`}>
                  {service.status === 'active' ? 'Aktif' : 'Pasif'}
                </span>
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-secondary-500">
                <div className="flex items-center space-x-2">
                  <span>{service.sort_order}</span>
                  <div className="flex flex-col space-y-1">
                    <button
                      onClick={() => onMove(service.id, 'up')}
                      disabled={index === 0}
                      className="p-1 text-secondary-400 hover:text-secondary-600 disabled:opacity-50"
                    >
                      <ArrowUpIcon className="h-3 w-3" />
                    </button>
                    <button
                      onClick={() => onMove(service.id, 'down')}
                      disabled={index === services.length - 1}
                      className="p-1 text-secondary-400 hover:text-secondary-600 disabled:opacity-50"
                    >
                      <ArrowDownIcon className="h-3 w-3" />
                    </button>
                  </div>
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <div className="flex items-center justify-end space-x-2">
                  <button
                    onClick={() => onEdit(service)}
                    className="text-primary-600 hover:text-primary-900"
                  >
                    <PencilIcon className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => onDelete(service.id)}
                    className="text-red-600 hover:text-red-900"
                  >
                    <TrashIcon className="h-4 w-4" />
                  </button>
                </div>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}

// Edit Modal Component
function EditModal({
  item,
  type,
  onSave,
  onClose
}: {
  item: HeroSlide | Service;
  type: 'hero' | 'services';
  onSave: (data: any) => void;
  onClose: () => void;
}) {
  const [formData, setFormData] = useState(item);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <Modal isOpen={true} onClose={onClose} title={`${item.id ? 'Düzenle' : 'Yeni Ekle'} - ${type === 'hero' ? 'Slider' : 'Hizmet'}`}>
      <form onSubmit={handleSubmit} className="space-y-6">
        {type === 'hero' ? (
          <>
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
              <div>
                <label className="block text-sm font-medium text-secondary-700">
                  Başlık (TR)
                </label>
                <input
                  type="text"
                  value={(formData as HeroSlide).title_tr}
                  onChange={(e) => handleInputChange('title_tr', e.target.value)}
                  className="mt-1 block w-full rounded-md border-secondary-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-secondary-700">
                  Başlık (EN)
                </label>
                <input
                  type="text"
                  value={(formData as HeroSlide).title_en}
                  onChange={(e) => handleInputChange('title_en', e.target.value)}
                  className="mt-1 block w-full rounded-md border-secondary-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                />
              </div>
            </div>
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
              <div>
                <label className="block text-sm font-medium text-secondary-700">
                  Alt Başlık (TR)
                </label>
                <textarea
                  rows={3}
                  value={(formData as HeroSlide).subtitle_tr}
                  onChange={(e) => handleInputChange('subtitle_tr', e.target.value)}
                  className="mt-1 block w-full rounded-md border-secondary-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-secondary-700">
                  Alt Başlık (EN)
                </label>
                <textarea
                  rows={3}
                  value={(formData as HeroSlide).subtitle_en}
                  onChange={(e) => handleInputChange('subtitle_en', e.target.value)}
                  className="mt-1 block w-full rounded-md border-secondary-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                />
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-secondary-700">
                Görsel URL
              </label>
              <input
                type="url"
                value={(formData as HeroSlide).image_path}
                onChange={(e) => handleInputChange('image_path', e.target.value)}
                className="mt-1 block w-full rounded-md border-secondary-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                placeholder="https://example.com/image.jpg"
              />
            </div>
          </>
        ) : (
          <>
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
              <div>
                <label className="block text-sm font-medium text-secondary-700">
                  Başlık (TR)
                </label>
                <input
                  type="text"
                  value={(formData as Service).title_tr}
                  onChange={(e) => handleInputChange('title_tr', e.target.value)}
                  className="mt-1 block w-full rounded-md border-secondary-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-secondary-700">
                  Başlık (EN)
                </label>
                <input
                  type="text"
                  value={(formData as Service).title_en}
                  onChange={(e) => handleInputChange('title_en', e.target.value)}
                  className="mt-1 block w-full rounded-md border-secondary-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                />
              </div>
            </div>
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
              <div>
                <label className="block text-sm font-medium text-secondary-700">
                  Açıklama (TR)
                </label>
                <textarea
                  rows={3}
                  value={(formData as Service).description_tr}
                  onChange={(e) => handleInputChange('description_tr', e.target.value)}
                  className="mt-1 block w-full rounded-md border-secondary-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-secondary-700">
                  Açıklama (EN)
                </label>
                <textarea
                  rows={3}
                  value={(formData as Service).description_en}
                  onChange={(e) => handleInputChange('description_en', e.target.value)}
                  className="mt-1 block w-full rounded-md border-secondary-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                />
              </div>
            </div>
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
              <div>
                <label className="block text-sm font-medium text-secondary-700">
                  İkon
                </label>
                <select
                  value={(formData as Service).icon}
                  onChange={(e) => handleInputChange('icon', e.target.value)}
                  className="mt-1 block w-full rounded-md border-secondary-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                >
                  <option value="atom">Atom</option>
                  <option value="zap">Zap</option>
                  <option value="beaker">Beaker</option>
                  <option value="microscope">Microscope</option>
                  <option value="code">Code</option>
                  <option value="chart">Chart</option>
                </select>
              </div>
              <div>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={(formData as Service).featured}
                    onChange={(e) => handleInputChange('featured', e.target.checked)}
                    className="rounded border-secondary-300 text-primary-600 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                  />
                  <span className="ml-2 text-sm text-secondary-700">Öne Çıkan</span>
                </label>
              </div>
            </div>
          </>
        )}

        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
          <div>
            <label className="block text-sm font-medium text-secondary-700">
              Sıra
            </label>
            <input
              type="number"
              value={formData.sort_order}
              onChange={(e) => handleInputChange('sort_order', parseInt(e.target.value))}
              className="mt-1 block w-full rounded-md border-secondary-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
              min="1"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-secondary-700">
              Durum
            </label>
            <select
              value={formData.status}
              onChange={(e) => handleInputChange('status', e.target.value)}
              className="mt-1 block w-full rounded-md border-secondary-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
            >
              <option value="active">Aktif</option>
              <option value="inactive">Pasif</option>
            </select>
          </div>
        </div>

        <div className="flex justify-end space-x-3">
          <Button
            type="button"
            onClick={onClose}
            className="bg-secondary-600 hover:bg-secondary-500"
          >
            İptal
          </Button>
          <Button
            type="submit"
            className="bg-primary-600 hover:bg-primary-500"
          >
            Kaydet
          </Button>
        </div>
      </form>
    </Modal>
  );
}
