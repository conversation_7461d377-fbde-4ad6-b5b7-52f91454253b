<?php
/**
 * RENERO Admin Panel - Authentication System
 * Secure session management and user authentication
 */

require_once 'database.php';

class Auth {
    private $db;


    public function __construct() {
        $this->db = new Database();
        $this->startSession();
    }
    
    private function startSession() {
        if (session_status() === PHP_SESSION_NONE) {
            ini_set('session.cookie_httponly', 1);
            ini_set('session.cookie_secure', 1);
            ini_set('session.use_strict_mode', 1);
            session_start();
        }
    }
    
    public function login($username, $password) {
        try {
            $user = $this->db->fetchOne(
                "SELECT * FROM admin_users WHERE (username = :username OR email = :username) AND status = 'active'",
                ['username' => $username]
            );

            if ($user && password_verify($password, $user['password'])) {
                $_SESSION['admin_id'] = $user['id'];
                $_SESSION['admin_username'] = $user['username'];
                $_SESSION['admin_email'] = $user['email'];
                $_SESSION['admin_role'] = $user['role'];
                $_SESSION['admin_name'] = $user['name'];
                $_SESSION['login_time'] = time();

                // Update last login
                $this->db->update(
                    'admin_users',
                    ['last_login' => date('Y-m-d H:i:s')],
                    'id = :id',
                    ['id' => $user['id']]
                );

                return true;
            }

            return false;
        } catch (Exception $e) {
            error_log("Login error: " . $e->getMessage());
            return false;
        }
    }
    
    public function logout() {
        session_destroy();
        return true;
    }
    
    public function isLoggedIn() {
        return isset($_SESSION['admin_id']) && !empty($_SESSION['admin_id']);
    }
    
    public function requireLogin() {
        if (!$this->isLoggedIn()) {
            if ($this->isAjaxRequest()) {
                jsonResponse(['error' => 'Authentication required'], 401);
            } else {
                header('Location: login.php');
                exit;
            }
        }
        
        // Check session timeout (2 hours)
        if (isset($_SESSION['login_time']) && (time() - $_SESSION['login_time']) > 7200) {
            $this->logout();
            if ($this->isAjaxRequest()) {
                jsonResponse(['error' => 'Session expired'], 401);
            } else {
                header('Location: login.php?expired=1');
                exit;
            }
        }
    }
    
    public function getCurrentUser() {
        if (!$this->isLoggedIn()) {
            return null;
        }
        
        return [
            'id' => $_SESSION['admin_id'],
            'username' => $_SESSION['admin_username'],
            'email' => $_SESSION['admin_email'],
            'role' => $_SESSION['admin_role'],
            'name' => $_SESSION['admin_name']
        ];
    }
    
    public function hasRole($role) {
        return isset($_SESSION['admin_role']) && $_SESSION['admin_role'] === $role;
    }
    
    public function isAdmin() {
        return $this->hasRole('admin');
    }
    
    public function isEditor() {
        return $this->hasRole('editor') || $this->isAdmin();
    }

    // API'lerde kullanılan alias metod
    public function isAuthenticated() {
        return $this->isLoggedIn();
    }
    
    private function isAjaxRequest() {
        return !empty($_SERVER['HTTP_X_REQUESTED_WITH']) &&
               strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest';
    }
}

// Global auth instance
$auth = new Auth();

// Global helper functions for API usage
function isAuthenticated() {
    global $auth;
    return $auth->isAuthenticated();
}

function getCurrentUser() {
    global $auth;
    return $auth->getCurrentUser();
}

function requireAuth() {
    global $auth;
    return $auth->requireLogin();
}