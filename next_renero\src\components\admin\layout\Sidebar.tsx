'use client';

import { Fragment, useState, useEffect } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import {
  XMarkIcon,
  HomeIcon,
  EnvelopeIcon,
  DocumentTextIcon,
  DocumentIcon,
  Cog6ToothIcon,
  ChevronDownIcon,
  BuildingOfficeIcon,
  PlusIcon,
} from '@heroicons/react/24/outline';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { useSettings } from '@/contexts/SettingsContext';
import { fetchFromAdminApi } from '@/utils/api';
import Image from 'next/image';
import clsx from 'clsx';

interface Page {
  id: number;
  slug: string;
  title_tr: string;
  title_en: string;
  status: string;
}

interface NavigationItem {
  name: string;
  href?: string;
  icon: any;
  children?: NavigationItem[];
  isDropdown?: boolean;
}

const navigation: NavigationItem[] = [
  { name: '<PERSON><PERSON><PERSON>i', href: '/admin/dashboard', icon: HomeIcon },
  { name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', href: '/admin/contacts', icon: EnvelopeIcon },
  { name: '<PERSON><PERSON><PERSON>', href: '/admin/blogs', icon: DocumentTextIcon },
  {
    name: 'Sayfalar',
    icon: DocumentIcon,
    isDropdown: true,
    children: [] // Bu dinamik olarak doldurulacak
  },
  { name: 'Şirket Bilgileri', href: '/admin/company-settings', icon: BuildingOfficeIcon },
  { name: 'Genel Ayarlar', href: '/admin/settings', icon: Cog6ToothIcon },
];

interface SidebarProps {
  open: boolean;
  setOpen: (open: boolean) => void;
}

export default function Sidebar({ open, setOpen }: SidebarProps) {
  const pathname = usePathname();
  const { user, logout } = useAuth();
  const { settings } = useSettings();
  const [pages, setPages] = useState<Page[]>([]);
  const [openDropdowns, setOpenDropdowns] = useState<string[]>(['Sayfalar']);

  // Sayfaları yükle
  useEffect(() => {
    const fetchPages = async () => {
      try {
        const response = await fetchFromAdminApi('/pages.php');
        if (response.success && response.data) {
          setPages(response.data);
        }
      } catch (error) {
        console.error('Sayfalar yüklenirken hata:', error);
      }
    };

    fetchPages();
  }, []);

  // Sayfalar dropdown'ını dinamik olarak güncelle
  useEffect(() => {
    const pagesNavItem = navigation.find(item => item.name === 'Sayfalar');
    if (pagesNavItem && pagesNavItem.children) {
      pagesNavItem.children = [
        { name: 'Ana Sayfa', href: '/admin/homepage', icon: HomeIcon },
        ...pages.map(page => ({
          name: page.title_tr,
          href: `/admin/pages/${page.slug}`,
          icon: DocumentIcon
        })),
        { name: '+ Yeni Sayfa Ekle', href: '/admin/pages/new', icon: PlusIcon }
      ];
    }
  }, [pages]);

  const toggleDropdown = (name: string) => {
    setOpenDropdowns(prev =>
      prev.includes(name)
        ? prev.filter(item => item !== name)
        : [...prev, name]
    );
  };

  return (
    <>
      {/* Mobile sidebar */}
      <Transition.Root show={open} as={Fragment}>
        <Dialog as="div" className="relative z-50 lg:hidden" onClose={setOpen}>
          <Transition.Child
            as={Fragment}
            enter="transition-opacity ease-linear duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="transition-opacity ease-linear duration-300"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-secondary-900/80" />
          </Transition.Child>

          <div className="fixed inset-0 flex">
            <Transition.Child
              as={Fragment}
              enter="transition ease-in-out duration-300 transform"
              enterFrom="-translate-x-full"
              enterTo="translate-x-0"
              leave="transition ease-in-out duration-300 transform"
              leaveFrom="translate-x-0"
              leaveTo="-translate-x-full"
            >
              <Dialog.Panel className="relative mr-16 flex w-full max-w-xs flex-1">
                <Transition.Child
                  as={Fragment}
                  enter="ease-in-out duration-300"
                  enterFrom="opacity-0"
                  enterTo="opacity-100"
                  leave="ease-in-out duration-300"
                  leaveFrom="opacity-100"
                  leaveTo="opacity-0"
                >
                  <div className="absolute left-full top-0 flex w-16 justify-center pt-5">
                    <button
                      type="button"
                      className="-m-2.5 p-2.5"
                      onClick={() => setOpen(false)}
                    >
                      <span className="sr-only">Close sidebar</span>
                      <XMarkIcon className="h-6 w-6 text-white" aria-hidden="true" />
                    </button>
                  </div>
                </Transition.Child>
                <div className="flex grow flex-col gap-y-5 overflow-y-auto bg-white px-6 pb-4">
                  <div className="flex h-16 shrink-0 items-center">
                    {settings?.site_logo ? (
                      <Image
                        className="h-8 w-auto"
                        src={settings.site_logo}
                        alt="RENERO"
                        width={32}
                        height={32}
                      />
                    ) : (
                      <div className="h-8 w-8 bg-primary-600 rounded flex items-center justify-center">
                        <span className="text-white font-bold text-sm">R</span>
                      </div>
                    )}
                    <div className="ml-3">
                      <h1 className="text-lg font-bold text-secondary-900">
                        {settings?.company_name || 'RENERO'}
                      </h1>
                      <p className="text-xs text-secondary-600 -mt-1">Admin Panel</p>
                    </div>
                  </div>
                  <nav className="flex flex-1 flex-col">
                    <ul role="list" className="flex flex-1 flex-col gap-y-7">
                      <li>
                        <ul role="list" className="-mx-2 space-y-1">
                          {navigation.map((item) => (
                            <li key={item.name}>
                              {item.isDropdown ? (
                                <div>
                                  <button
                                    onClick={() => toggleDropdown(item.name)}
                                    className="group flex w-full gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold text-secondary-700 hover:bg-secondary-50 hover:text-primary-600"
                                  >
                                    <item.icon
                                      className="h-6 w-6 shrink-0 text-secondary-400 group-hover:text-primary-600"
                                      aria-hidden="true"
                                    />
                                    {item.name}
                                    <ChevronDownIcon
                                      className={clsx(
                                        'ml-auto h-5 w-5 transition-transform',
                                        openDropdowns.includes(item.name) ? 'rotate-180' : ''
                                      )}
                                    />
                                  </button>
                                  {openDropdowns.includes(item.name) && item.children && (
                                    <ul className="mt-1 ml-6 space-y-1">
                                      {item.children.map((child) => (
                                        <li key={child.name}>
                                          <Link
                                            href={child.href || '#'}
                                            className={clsx(
                                              pathname === child.href
                                                ? 'sidebar-nav-item-active'
                                                : 'sidebar-nav-item-inactive',
                                              'group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold'
                                            )}
                                            onClick={() => setOpen(false)}
                                          >
                                            <child.icon
                                              className={clsx(
                                                pathname === child.href
                                                  ? 'text-primary-600'
                                                  : 'text-secondary-400 group-hover:text-primary-600',
                                                'h-5 w-5 shrink-0'
                                              )}
                                              aria-hidden="true"
                                            />
                                            {child.name}
                                          </Link>
                                        </li>
                                      ))}
                                    </ul>
                                  )}
                                </div>
                              ) : (
                                <Link
                                  href={item.href || '#'}
                                  className={clsx(
                                    pathname === item.href
                                      ? 'sidebar-nav-item-active'
                                      : 'sidebar-nav-item-inactive',
                                    'group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold'
                                  )}
                                  onClick={() => setOpen(false)}
                                >
                                  <item.icon
                                    className={clsx(
                                      pathname === item.href
                                        ? 'text-primary-600'
                                        : 'text-secondary-400 group-hover:text-primary-600',
                                      'h-6 w-6 shrink-0'
                                    )}
                                    aria-hidden="true"
                                  />
                                  {item.name}
                                </Link>
                              )}
                            </li>
                          ))}
                        </ul>
                      </li>
                      <li className="mt-auto">
                        <div className="flex items-center gap-x-4 px-2 py-3 text-sm font-semibold leading-6 text-secondary-900">
                          <div className="h-8 w-8 rounded-full bg-primary-600 flex items-center justify-center">
                            <span className="text-sm font-medium text-white">
                              {user?.name?.charAt(0).toUpperCase()}
                            </span>
                          </div>
                          <span className="sr-only">Your profile</span>
                          <div className="flex-1">
                            <p className="text-sm font-medium text-secondary-900">{user?.name}</p>
                            <p className="text-xs text-secondary-500">{user?.email}</p>
                          </div>
                        </div>
                        <button
                          onClick={logout}
                          className="group -mx-2 flex w-full gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold text-secondary-700 hover:bg-secondary-50 hover:text-primary-600"
                        >
                          Çıkış Yap
                        </button>
                      </li>
                    </ul>
                  </nav>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </Dialog>
      </Transition.Root>

      {/* Static sidebar for desktop */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-64 lg:flex-col">
        <div className="flex grow flex-col gap-y-5 overflow-y-auto border-r border-secondary-200 bg-white px-5 pb-4">
          <div className="flex h-16 shrink-0 items-center">
            {settings?.site_logo && settings.site_logo.trim() !== '' ? (
              <Image
                className="h-8 w-auto"
                src={settings.site_logo}
                alt="RENERO"
                width={32}
                height={32}
              />
            ) : (
              <div className="h-8 w-8 bg-primary-600 rounded flex items-center justify-center">
                <span className="text-white font-bold text-sm">R</span>
              </div>
            )}
            <div className="ml-3">
              <h1 className="text-lg font-bold text-secondary-900">
                RENERO
              </h1>
              <p className="text-xs text-secondary-600 -mt-1">Admin Panel</p>
            </div>
          </div>

          {/* Navigation arasına boşluk ekledik */}
          <div className="pt-8">
            <nav className="flex flex-1 flex-col">
            <ul role="list" className="flex flex-1 flex-col gap-y-7">
              <li>
                <ul role="list" className="-mx-2 space-y-1">
                  {navigation.map((item) => (
                    <li key={item.name}>
                      {item.isDropdown ? (
                        <div>
                          <button
                            onClick={() => toggleDropdown(item.name)}
                            className="group flex w-full gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold text-secondary-700 hover:bg-secondary-50 hover:text-primary-600"
                          >
                            <item.icon
                              className="h-6 w-6 shrink-0 text-secondary-400 group-hover:text-primary-600"
                              aria-hidden="true"
                            />
                            {item.name}
                            <ChevronDownIcon
                              className={clsx(
                                'ml-auto h-5 w-5 transition-transform',
                                openDropdowns.includes(item.name) ? 'rotate-180' : ''
                              )}
                            />
                          </button>
                          {openDropdowns.includes(item.name) && item.children && (
                            <ul className="mt-1 ml-6 space-y-1">
                              {item.children.map((child) => (
                                <li key={child.name}>
                                  <Link
                                    href={child.href || '#'}
                                    className={clsx(
                                      pathname === child.href
                                        ? 'sidebar-nav-item-active'
                                        : 'sidebar-nav-item-inactive',
                                      'group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold'
                                    )}
                                  >
                                    <child.icon
                                      className={clsx(
                                        pathname === child.href
                                          ? 'text-primary-600'
                                          : 'text-secondary-400 group-hover:text-primary-600',
                                        'h-5 w-5 shrink-0'
                                      )}
                                      aria-hidden="true"
                                    />
                                    {child.name}
                                  </Link>
                                </li>
                              ))}
                            </ul>
                          )}
                        </div>
                      ) : (
                        <Link
                          href={item.href || '#'}
                          className={clsx(
                            pathname === item.href
                              ? 'sidebar-nav-item-active'
                              : 'sidebar-nav-item-inactive',
                            'group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold'
                          )}
                        >
                          <item.icon
                            className={clsx(
                              pathname === item.href
                                ? 'text-primary-600'
                                : 'text-secondary-400 group-hover:text-primary-600',
                              'h-6 w-6 shrink-0'
                            )}
                            aria-hidden="true"
                          />
                          {item.name}
                        </Link>
                      )}
                    </li>
                  ))}
                </ul>
              </li>
              <li className="mt-auto">
                <div className="flex items-center gap-x-4 px-2 py-3 text-sm font-semibold leading-6 text-secondary-900">
                  <div className="h-8 w-8 rounded-full bg-primary-600 flex items-center justify-center">
                    <span className="text-sm font-medium text-white">
                      {user?.name?.charAt(0).toUpperCase()}
                    </span>
                  </div>
                  <span className="sr-only">Your profile</span>
                  <div className="flex-1">
                    <p className="text-sm font-medium text-secondary-900">{user?.name}</p>
                    <p className="text-xs text-secondary-500">{user?.email}</p>
                  </div>
                </div>
                <button
                  onClick={logout}
                  className="group -mx-2 flex w-full gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold text-secondary-700 hover:bg-secondary-50 hover:text-primary-600"
                >
                  Çıkış Yap
                </button>
              </li>
            </ul>
            </nav>
          </div>
        </div>
      </div>
    </>
  );
}
