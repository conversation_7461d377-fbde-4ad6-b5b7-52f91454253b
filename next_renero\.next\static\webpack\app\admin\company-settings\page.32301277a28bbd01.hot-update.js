"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/company-settings/page",{

/***/ "(app-pages-browser)/./src/utils/api.ts":
/*!**************************!*\
  !*** ./src/utils/api.ts ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fetchFromAdminApi: () => (/* binding */ fetchFromAdminApi),\n/* harmony export */   fetchFromApi: () => (/* binding */ fetchFromApi),\n/* harmony export */   getApiUrl: () => (/* binding */ getApiUrl)\n/* harmony export */ });\n/* __next_internal_client_entry_do_not_use__ getApiUrl,fetchFromApi,fetchFromAdminApi auto */ /**\n * API istekleri için yardımcı fonksiyon\n * @param endpoint - API endpoint yolu (örn: '/navigation.php')\n * @param params - URL parametreleri\n * @returns Tam API URL'si\n */ function getApiUrl(endpoint, params) {\n    // BASE_API_URL değerini çevre değişkeninden al - main API için\n    const baseApiUrl = \"http://localhost:3000/api\" || (0);\n    // Endpoint'i tam URL'ye dönüştür\n    let url = \"\".concat(baseApiUrl).concat(endpoint);\n    // Parametreler varsa URL'ye ekle\n    if (params && Object.keys(params).length > 0) {\n        const queryString = Object.entries(params).map((param)=>{\n            let [key, value] = param;\n            return \"\".concat(encodeURIComponent(key), \"=\").concat(encodeURIComponent(value));\n        }).join('&');\n        url = \"\".concat(url, \"?\").concat(queryString);\n    }\n    return url;\n}\n/**\n * API'den veri getirmek için yardımcı fonksiyon\n * @param endpoint - API endpoint yolu (örn: '/navigation.php')\n * @param params - URL parametreleri\n * @returns API yanıtı\n */ async function fetchFromApi(endpoint, params) {\n    // Mock veri sistemini devre dışı bırak - gerçek API kullan\n    console.log('Production mode: Using real API for', endpoint);\n    const url = getApiUrl(endpoint, params);\n    try {\n        const response = await fetch(url);\n        if (!response.ok) {\n            throw new Error(\"API error: \".concat(response.status));\n        }\n        const data = await response.json();\n        return data;\n    } catch (error) {\n        console.error('API fetch error:', error);\n        throw error;\n    }\n}\n/**\n * Admin API istekleri için yardımcı fonksiyon\n * @param endpoint - API endpoint yolu (örn: '/users.php')\n * @param options - Fetch options (method, body, headers)\n * @returns API yanıtı\n */ async function fetchFromAdminApi(endpoint, options) {\n    // Admin API base URL\n    const baseApiUrl =  false ? 0 : 'http://localhost:8080/admin';\n    const url = \"\".concat(baseApiUrl).concat(endpoint);\n    console.log('Admin API request:', url);\n    try {\n        const response = await fetch(url, {\n            headers: {\n                'Content-Type': 'application/json',\n                ...options === null || options === void 0 ? void 0 : options.headers\n            },\n            ...options\n        });\n        if (!response.ok) {\n            throw new Error(\"Admin API error: \".concat(response.status));\n        }\n        const data = await response.json();\n        return data;\n    } catch (error) {\n        console.error('Admin API fetch error:', error);\n        throw error;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/api.ts\n"));

/***/ })

});