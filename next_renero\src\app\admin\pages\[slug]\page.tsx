'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import WithAuth from '@/components/admin/auth/WithAuth';
import AdminLayout from '@/components/admin/layout/AdminLayout';
import { fetchFromAdminApi } from '@/utils/api';
import toast from 'react-hot-toast';
import { Button } from '@/components/ui/Button';
import { ArrowLeftIcon } from '@heroicons/react/24/outline';

interface Page {
  id: number;
  slug: string;
  title_tr: string;
  title_en: string;
  content_tr: string;
  content_en: string;
  meta_description_tr: string;
  meta_description_en: string;
  status: string;
}

export default function EditPagePage() {
  return (
    <WithAuth>
      <EditPageContent />
    </WithAuth>
  );
}

function EditPageContent() {
  const params = useParams();
  const router = useRouter();
  const slug = params.slug as string;
  
  const [page, setPage] = useState<Page>({
    id: 0,
    slug: '',
    title_tr: '',
    title_en: '',
    content_tr: '',
    content_en: '',
    meta_description_tr: '',
    meta_description_en: '',
    status: 'active'
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    if (slug && slug !== 'new') {
      fetchPage();
    } else {
      setLoading(false);
    }
  }, [slug]);

  const fetchPage = async () => {
    try {
      const response = await fetchFromAdminApi(`/pages.php?slug=${slug}`);
      if (response.success && response.data) {
        setPage(response.data);
      } else {
        toast.error('Sayfa bulunamadı');
        router.push('/admin/pages');
      }
    } catch (error) {
      console.error('Sayfa yüklenirken hata:', error);
      toast.error('Sayfa yüklenirken hata oluştu');
      router.push('/admin/pages');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof Page, value: string) => {
    setPage(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSave = async () => {
    setSaving(true);
    try {
      const method = page.id ? 'PUT' : 'POST';
      const response = await fetchFromAdminApi('/pages.php', {
        method,
        body: JSON.stringify(page)
      });

      if (response.success) {
        toast.success(page.id ? 'Sayfa güncellendi' : 'Sayfa oluşturuldu');
        if (!page.id && response.data?.slug) {
          router.push(`/admin/pages/${response.data.slug}`);
        }
      } else {
        toast.error(response.message || 'Kaydetme işlemi başarısız');
      }
    } catch (error) {
      console.error('Kaydetme hatası:', error);
      toast.error('Kaydetme sırasında hata oluştu');
    } finally {
      setSaving(false);
    }
  };

  const generateSlug = (title: string) => {
    return title
      .toLowerCase()
      .replace(/ğ/g, 'g')
      .replace(/ü/g, 'u')
      .replace(/ş/g, 's')
      .replace(/ı/g, 'i')
      .replace(/ö/g, 'o')
      .replace(/ç/g, 'c')
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim('-');
  };

  const handleTitleChange = (value: string) => {
    handleInputChange('title_tr', value);
    if (!page.id && !page.slug) {
      handleInputChange('slug', generateSlug(value));
    }
  };

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="px-4 sm:px-6 lg:px-8 py-8">
        <div className="sm:flex sm:items-center">
          <div className="sm:flex-auto">
            <div className="flex items-center space-x-3">
              <button
                onClick={() => router.back()}
                className="p-2 text-secondary-400 hover:text-secondary-600"
              >
                <ArrowLeftIcon className="h-5 w-5" />
              </button>
              <div>
                <h1 className="text-xl font-semibold text-secondary-900">
                  {page.id ? 'Sayfa Düzenle' : 'Yeni Sayfa'}
                </h1>
                <p className="mt-2 text-sm text-secondary-700">
                  {page.id ? `${page.title_tr} sayfasını düzenleyin` : 'Yeni bir sayfa oluşturun'}
                </p>
              </div>
            </div>
          </div>
          <div className="mt-4 sm:ml-16 sm:mt-0 sm:flex-none">
            <Button
              onClick={handleSave}
              disabled={saving}
              className="bg-primary-600 hover:bg-primary-500"
            >
              {saving ? 'Kaydediliyor...' : 'Kaydet'}
            </Button>
          </div>
        </div>

        <div className="mt-8 space-y-8">
          {/* Temel Bilgiler */}
          <div className="bg-white shadow rounded-lg p-6">
            <h2 className="text-lg font-medium text-secondary-900 mb-4">Temel Bilgiler</h2>
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
              <div>
                <label className="block text-sm font-medium text-secondary-700">
                  Başlık (Türkçe) *
                </label>
                <input
                  type="text"
                  value={page.title_tr}
                  onChange={(e) => handleTitleChange(e.target.value)}
                  className="mt-1 block w-full rounded-md border-secondary-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-secondary-700">
                  Başlık (İngilizce)
                </label>
                <input
                  type="text"
                  value={page.title_en}
                  onChange={(e) => handleInputChange('title_en', e.target.value)}
                  className="mt-1 block w-full rounded-md border-secondary-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-secondary-700">
                  URL Slug *
                </label>
                <input
                  type="text"
                  value={page.slug}
                  onChange={(e) => handleInputChange('slug', e.target.value)}
                  className="mt-1 block w-full rounded-md border-secondary-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                  placeholder="sayfa-url"
                  required
                />
                <p className="mt-1 text-sm text-secondary-500">
                  Sayfanın URL'inde görünecek kısım (örn: hakkimizda)
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-secondary-700">
                  Durum
                </label>
                <select
                  value={page.status}
                  onChange={(e) => handleInputChange('status', e.target.value)}
                  className="mt-1 block w-full rounded-md border-secondary-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                >
                  <option value="active">Aktif</option>
                  <option value="inactive">Pasif</option>
                </select>
              </div>
            </div>
          </div>

          {/* İçerik */}
          <div className="bg-white shadow rounded-lg p-6">
            <h2 className="text-lg font-medium text-secondary-900 mb-4">İçerik</h2>
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-secondary-700">
                  İçerik (Türkçe)
                </label>
                <textarea
                  rows={12}
                  value={page.content_tr}
                  onChange={(e) => handleInputChange('content_tr', e.target.value)}
                  className="mt-1 block w-full rounded-md border-secondary-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                  placeholder="HTML içerik girebilirsiniz..."
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-secondary-700">
                  İçerik (İngilizce)
                </label>
                <textarea
                  rows={12}
                  value={page.content_en}
                  onChange={(e) => handleInputChange('content_en', e.target.value)}
                  className="mt-1 block w-full rounded-md border-secondary-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                  placeholder="HTML content can be entered..."
                />
              </div>
            </div>
          </div>

          {/* SEO */}
          <div className="bg-white shadow rounded-lg p-6">
            <h2 className="text-lg font-medium text-secondary-900 mb-4">SEO Ayarları</h2>
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
              <div>
                <label className="block text-sm font-medium text-secondary-700">
                  Meta Açıklama (Türkçe)
                </label>
                <textarea
                  rows={3}
                  value={page.meta_description_tr}
                  onChange={(e) => handleInputChange('meta_description_tr', e.target.value)}
                  className="mt-1 block w-full rounded-md border-secondary-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                  maxLength={160}
                />
                <p className="mt-1 text-sm text-secondary-500">
                  {page.meta_description_tr.length}/160 karakter
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-secondary-700">
                  Meta Açıklama (İngilizce)
                </label>
                <textarea
                  rows={3}
                  value={page.meta_description_en}
                  onChange={(e) => handleInputChange('meta_description_en', e.target.value)}
                  className="mt-1 block w-full rounded-md border-secondary-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                  maxLength={160}
                />
                <p className="mt-1 text-sm text-secondary-500">
                  {page.meta_description_en.length}/160 characters
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}
