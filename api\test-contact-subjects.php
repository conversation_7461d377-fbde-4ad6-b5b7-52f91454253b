<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');

try {
    echo "Testing contact-subjects.php...\n";
    
    // Test database path
    $dbPath = __DIR__ . '/../admin/config/database.php';
    echo "Database path: $dbPath\n";
    echo "File exists: " . (file_exists($dbPath) ? 'YES' : 'NO') . "\n";
    
    if (file_exists($dbPath)) {
        require_once $dbPath;
        echo "Database file loaded successfully\n";
        
        $db = new Database();
        echo "Database connection created\n";
        
        $sql = "SELECT id, name_tr, name_en, status, sort_order FROM contact_subjects WHERE status = 'active' ORDER BY sort_order, name_tr LIMIT 5";
        $subjects = $db->fetchAll($sql);
        echo "Query executed successfully\n";
        echo "Found " . count($subjects) . " subjects\n";
        
        foreach ($subjects as $subject) {
            echo "- {$subject['name_tr']} ({$subject['name_en']})\n";
        }
        
    } else {
        echo "Database file not found!\n";
    }
    
} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
}
